const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
require('dotenv').config();

console.log('Loading routes...');

let authRoutes, attendanceRoutes, analyticsRoutes, notificationRoutes;

try {
  authRoutes = require('./routes/authRoutes');
  console.log('✅ Auth routes loaded');

  attendanceRoutes = require('./routes/attendanceRoutes');
  console.log('✅ Attendance routes loaded');

  analyticsRoutes = require('./routes/analyticsRoutes');
  console.log('✅ Analytics routes loaded');

  notificationRoutes = require('./routes/notificationRoutes');
  console.log('✅ Notification routes loaded');
} catch (error) {
  console.error('❌ Error loading routes:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}

const app = express();

app.use(cors());
app.use(bodyParser.json());

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  if (req.headers.authorization) {
    console.log('  - Auth header present:', req.headers.authorization.substring(0, 20) + '...');
  }
  next();
});

app.use('/auth', authRoutes);
app.use('/attendance', attendanceRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/notifications', notificationRoutes);

// Test route 
app.get('/', async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Smart Attendance System API is running',
      environment: process.env.NODE_ENV || 'development',
      time: new Date().toISOString()
    });
  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Placeholder for routes
// app.use('/auth', authRoutes);
// app.use('/users', userRoutes);

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

const PORT = process.env.PORT || 5001;

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server is running on http://localhost:${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Time: ${new Date().toISOString()}`);
});

server.on('error', (error) => {
  console.error('Server error:', error);
});
