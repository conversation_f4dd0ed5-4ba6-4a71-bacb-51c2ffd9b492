import React from "react";
import { <PERSON> } from "react-router-dom";
import { Facebook, Twitter, Linkedin, Github } from "lucide-react";

export default function Footer({ role }) {
  return (
    <footer className="bg-blue-50 text-gray-800 mt-12 border-t border-blue-200">
      {/* Container */}
      <div className="max-w-7xl mx-auto px-6 py-10 text-center">
        {/* Brand + Description */}
        <div>
          <h2 className="text-2xl font-bold tracking-tight text-blue-800">
            Smart Attendee
          </h2>
          <p className="mt-3 text-gray-700 max-w-xl mx-auto leading-relaxed">
            A smart QR-powered attendance system for students, faculty, and admins.
          </p>

          {/* Social Icons */}
          <div className="flex justify-center space-x-5 mt-6">
            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors"><Facebook size={22} /></a>
            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors"><Twitter size={22} /></a>
            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors"><Linkedin size={22} /></a>
            <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors"><Github size={22} /></a>
          </div>
        </div>

        {/* Links Section */}
        <div className="mt-10 flex justify-center">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4 text-blue-800">Quick Links</h3>
            <ul className="flex flex-wrap justify-center gap-6 text-gray-700">
              <li><Link to="/about" className="hover:text-blue-600">About Us</Link></li>
              <li><Link to="/contact" className="hover:text-blue-600">Contact</Link></li>
              <li><Link to="/faq" className="hover:text-blue-600">FAQs</Link></li>
              <li><Link to="/privacy" className="hover:text-blue-600">Privacy Policy</Link></li>
              <li><Link to="/terms" className="hover:text-blue-600">Terms of Service</Link></li>
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-blue-100 text-center py-4 text-gray-700 text-sm border-t border-blue-200">
        © {new Date().getFullYear()}{" "}
        <span className="font-semibold text-blue-800">SmartAttendee</span>. All rights reserved.
      </div>
    </footer>
  );
}
