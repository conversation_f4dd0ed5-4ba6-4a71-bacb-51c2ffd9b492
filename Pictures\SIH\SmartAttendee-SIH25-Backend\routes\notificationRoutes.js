const express = require('express');
const router = express.Router();
const authenticateToken = require('../middleware/auth');
const permit = require('../middleware/rbac');
const notificationController = require('../controllers/notificationController');

//for admin to send parent notifications
router.post('/send-summary', authenticateToken, permit('admin'), notificationController.sendParentSummary);

module.exports = router;
