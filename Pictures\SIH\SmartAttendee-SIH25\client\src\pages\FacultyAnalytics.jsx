import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TrendingUp,
  Users,
  AlertTriangle,
  Calendar,
  RefreshCw,
  ArrowLeft,
  BookOpen,
  MessageCircle,
  UserCheck,
  Phone,
  Target,
  BarChart3,
  Pie<PERSON>hart
} from 'lucide-react';

const FacultyAnalyticsPage = () => {
  const navigate = useNavigate();
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('authToken') || localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch('https://smartattendee-sih25-backend.onrender.com/api/analytics/faculty', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('authToken');
          localStorage.removeItem('token');
          navigate('/login');
          return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setAnalyticsData(data);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err.message || 'Failed to load analytics data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading faculty analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Analytics</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchAnalyticsData}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center mx-auto"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Analytics Data</h2>
          <p className="text-gray-600">No analytics data available at this time.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <button
              onClick={() => navigate('/faculty')}
              className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              📊 Faculty Analytics Dashboard
            </h1>
            <p className="text-gray-600">
              Welcome {analyticsData.facultyName}, here's your comprehensive analytics overview
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Generated at: {new Date(analyticsData.generatedAt).toLocaleString()}
            </p>
          </div>
          <button
            onClick={fetchAnalyticsData}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Data
          </button>
        </div>

        {/* Overview Cards */}
        <OverviewCards analyticsData={analyticsData} />

        {/* Classes Summary */}
        <ClassesSummary classes={analyticsData.classes || []} />

        {/* Charts Section */}
        <ChartsSection
          subjectBarData={analyticsData.subjectBarData || []}
          subjectPieData={analyticsData.subjectPieData || {}}
          classes={analyticsData.classes || []}
        />

        {/* Comparison with Other Classes */}
        <ClassComparisonSection
          classComparison={analyticsData.classComparison || []}
          performanceThreshold={analyticsData.performanceThreshold || {}}
        />

        {/* At-Risk Students */}
        <AtRiskStudents classes={analyticsData.classes || []} />

        {/* Engagement & Suggestions */}
        <EngagementSection
          engagement={analyticsData.engagement || []}
          suggestions={analyticsData.suggestions || []}
        />
      </div>
    </div>
  );
};

// Overview Cards Component
const OverviewCards = ({ analyticsData }) => {
  const totalClasses = analyticsData.classes?.length || 0;
  const totalStudents = analyticsData.classes?.reduce((sum, cls) => sum + (cls.totalStudents || 0), 0) || 0;
  const avgAttendance = totalClasses > 0
    ? (analyticsData.classes.reduce((sum, cls) => sum + (cls.overall?.attendancePct || 0), 0) / totalClasses).toFixed(1)
    : 0;
  const atRiskStudents = analyticsData.classes?.reduce((sum, cls) => sum + (cls.atRiskStudents?.length || 0), 0) || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Total Classes</dt>
                <dd className="text-2xl font-bold text-gray-900">{totalClasses}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Total Students</dt>
                <dd className="text-2xl font-bold text-gray-900">{totalStudents}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Avg Attendance</dt>
                <dd className="text-2xl font-bold text-gray-900">{avgAttendance}%</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">At-Risk Students</dt>
                <dd className="text-2xl font-bold text-gray-900">{atRiskStudents}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Classes Summary Component
const ClassesSummary = ({ classes }) => {
  return (
    <div className="bg-white shadow rounded-lg mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <BookOpen className="h-5 w-5 mr-2" />
          Classes Overview
        </h3>
      </div>
      <div className="p-6">
        {classes.length > 0 ? (
          <div className="space-y-6">
            {classes.map((cls, index) => (
              <div key={cls.classId || index} className="border border-gray-200 rounded-lg p-6">
                {/* Class Header */}
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-xl font-semibold text-gray-900">{cls.className}</h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {cls.totalStudents} students
                    </span>
                    <span className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {cls.overall?.totalSessions || 0} sessions
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      (cls.overall?.attendancePct || 0) >= 80
                        ? 'bg-green-100 text-green-800'
                        : (cls.overall?.attendancePct || 0) >= 60
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {(cls.overall?.attendancePct || 0).toFixed(1)}% Overall
                    </span>
                  </div>
                </div>

                {/* Subjects Table */}
                {cls.subjects && cls.subjects.length > 0 && (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Subject
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Sessions
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Present Count
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Attendance %
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {cls.subjects.map((subject, subIndex) => (
                          <tr key={subject.subjectId || subIndex}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {subject.subjectName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {subject.totalSessions || 0}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {subject.presentCount || 0} / {subject.possibleSeats || 0}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                (subject.attendancePct || 0) >= 80
                                  ? 'bg-green-100 text-green-800'
                                  : (subject.attendancePct || 0) >= 60
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {(subject.attendancePct || 0).toFixed(1)}%
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}

                {/* Last 7 Days Trend */}
                {cls.last7days && cls.last7days.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h5 className="text-sm font-medium text-gray-900 mb-2">Last 7 Days Trend</h5>
                    <div className="flex space-x-2">
                      {cls.last7days.map((day, dayIndex) => (
                        <div key={dayIndex} className="flex-1 text-center">
                          <div className="text-xs text-gray-500 mb-1">
                            {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </div>
                          <div className={`h-8 rounded flex items-center justify-center text-xs font-medium ${
                            (day.attendancePct || 0) >= 80
                              ? 'bg-green-100 text-green-800'
                              : (day.attendancePct || 0) >= 60
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {(day.attendancePct || 0).toFixed(0)}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">No classes data available</p>
        )}
      </div>
    </div>
  );
};

// Charts Section Component
const ChartsSection = ({ subjectBarData, subjectPieData, classes }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      {/* Subject Comparison Bar Chart */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Subject Attendance Comparison
        </h3>
        {subjectBarData.length > 0 ? (
          <div className="space-y-4">
            {subjectBarData.map((subject, index) => (
              <div key={subject.subjectId || index} className="flex items-center">
                <div className="w-32 text-sm text-gray-600 truncate">
                  {subject.subjectName}
                </div>
                <div className="flex-1 mx-4">
                  <div className="bg-gray-200 rounded-full h-4">
                    <div
                      className={`h-4 rounded-full ${
                        (subject.avgAttendancePct || 0) >= 80
                          ? 'bg-green-500'
                          : (subject.avgAttendancePct || 0) >= 60
                          ? 'bg-yellow-500'
                          : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(subject.avgAttendancePct || 0, 100)}%` }}
                    ></div>
                  </div>
                </div>
                <div className="w-16 text-sm font-medium text-gray-900 text-right">
                  {(subject.avgAttendancePct || 0).toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">No subject data available</p>
        )}
      </div>

      {/* Subject Distribution Pie Charts */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <PieChart className="h-5 w-5 mr-2" />
          Subject Distribution
        </h3>
        {Object.keys(subjectPieData).length > 0 ? (
          <div className="space-y-6">
            {Object.values(subjectPieData).map((subject, index) => (
              <div key={subject.subjectId || index}>
                <h4 className="text-sm font-medium text-gray-700 mb-2">{subject.subjectName}</h4>
                <div className="flex items-center space-x-4">
                  {subject.labels?.map((label, labelIndex) => (
                    <div key={labelIndex} className="flex items-center">
                      <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                      <span className="text-sm text-gray-600">{label}: {subject.data?.[labelIndex] || 0}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">No distribution data available</p>
        )}
      </div>
    </div>
  );
};

// At-Risk Students Component
const AtRiskStudents = ({ classes }) => {
  const allAtRiskStudents = classes.flatMap(cls =>
    (cls.atRiskStudents || []).map(student => ({
      ...student,
      className: cls.className
    }))
  );

  const handleAction = (action, student) => {
    console.log(`${action} action for student:`, student);
    // Implement actual actions here
    alert(`${action} action for ${student.studentName} - Feature coming soon!`);
  };

  return (
    <div className="bg-white shadow rounded-lg mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
          At-Risk Students ({allAtRiskStudents.length})
        </h3>
      </div>
      <div className="p-6">
        {allAtRiskStudents.length > 0 ? (
          <div className="space-y-4">
            {allAtRiskStudents.map((student, index) => (
              <div key={student.studentId || index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900">{student.studentName}</h4>
                      <span className="text-sm text-gray-500">({student.enrollmentNo})</span>
                      <span className="text-sm text-gray-500">- {student.className}</span>
                    </div>
                    <div className="flex items-center space-x-4 mb-3">
                      <span className="text-sm text-red-600 font-medium">
                        Overall: {(student.overallPct || 0).toFixed(1)}%
                      </span>
                    </div>
                    {student.weakSubjects && student.weakSubjects.length > 0 && (
                      <div className="mb-3">
                        <span className="text-sm text-gray-600 mr-2">Weak subjects:</span>
                        <div className="flex flex-wrap gap-2">
                          {student.weakSubjects.map((subject, subIndex) => (
                            <span
                              key={subject.subjectId || subIndex}
                              className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full"
                            >
                              {subject.subjectName}: {(subject.pct || 0).toFixed(1)}%
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => handleAction('Message', student)}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors flex items-center"
                    >
                      <MessageCircle className="h-3 w-3 mr-1" />
                      Message
                    </button>
                    <button
                      onClick={() => handleAction('Schedule Meeting', student)}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors flex items-center"
                    >
                      <Calendar className="h-3 w-3 mr-1" />
                      Meeting
                    </button>
                    <button
                      onClick={() => handleAction('Notify Parent', student)}
                      className="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors flex items-center"
                    >
                      <Phone className="h-3 w-3 mr-1" />
                      Parent
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <UserCheck className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-gray-500">No at-risk students found. Great job!</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Engagement Section Component
const EngagementSection = ({ engagement, suggestions }) => {
  const handleSuggestionAction = (suggestion, index) => {
    console.log('Suggestion action:', suggestion);
    alert(`Action for suggestion ${index + 1} - Feature coming soon!`);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Engagement Scores */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Target className="h-5 w-5 mr-2" />
          Class Engagement Scores
        </h3>
        {engagement.length > 0 ? (
          <div className="space-y-4">
            {engagement
              .sort((a, b) => (a.engagementScore || 0) - (b.engagementScore || 0))
              .map((cls, index) => (
                <div key={cls.classId || index} className="flex items-center">
                  <div className="w-32 text-sm text-gray-600 truncate">
                    {cls.className}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="bg-gray-200 rounded-full h-4">
                      <div
                        className={`h-4 rounded-full ${
                          (cls.engagementScore || 0) >= 80
                            ? 'bg-green-500'
                            : (cls.engagementScore || 0) >= 60
                            ? 'bg-yellow-500'
                            : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(cls.engagementScore || 0, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-16 text-sm font-medium text-gray-900 text-right">
                    {(cls.engagementScore || 0).toFixed(1)}
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">No engagement data available</p>
        )}
      </div>

      {/* Suggestions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <TrendingUp className="h-5 w-5 mr-2" />
          Improvement Suggestions
        </h3>
        {suggestions.length > 0 ? (
          <div className="space-y-3">
            {suggestions.map((suggestion, index) => (
              <div key={index} className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                <p className="text-sm text-gray-700 mb-3">{suggestion}</p>
                <button
                  onClick={() => handleSuggestionAction(suggestion, index)}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  Take Action
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-gray-500">All classes performing well!</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Class Comparison Section Component
const ClassComparisonSection = ({ classComparison, performanceThreshold }) => {
  // Sort classes by attendance percentage for better visualization
  const sortedClasses = [...classComparison].sort((a, b) => (b.attendancePct || 0) - (a.attendancePct || 0));

  // Prepare data for performance threshold pie chart
  const pieChartData = {
    labels: ['Above Threshold', 'Below Threshold'],
    data: [performanceThreshold.aboveThreshold || 0, performanceThreshold.belowThreshold || 0],
    colors: ['#10B981', '#EF4444'], // green for above, red for below
    total: performanceThreshold.total || 0
  };

  return (
    <div className="bg-white shadow rounded-lg mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-xl font-bold text-gray-900 text-center">
          Comparison with Other Classes
        </h3>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          {/* Class Comparison - Attendance Bar Chart */}
          <div className="space-y-4">
            <div className="text-center">
              <h4 className="text-lg font-semibold text-gray-800 mb-2">Class Comparison - Attendance</h4>
              <p className="text-sm text-gray-600">Attendance Percentage</p>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {sortedClasses.length > 0 ? (
                sortedClasses.map((cls, index) => (
                  <div key={cls.classId || index} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className={`font-medium truncate ${cls.isOwnClass ? 'text-blue-600' : 'text-gray-700'}`}>
                        {cls.className} {cls.isOwnClass && '(Your Class)'}
                      </span>
                      <span className="text-gray-600 ml-2">
                        {(cls.attendancePct || 0).toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-6">
                      <div
                        className={`h-6 rounded-full transition-all duration-300 ${
                          cls.isOwnClass
                            ? 'bg-blue-500'
                            : (cls.attendancePct || 0) >= 80
                            ? 'bg-green-500'
                            : (cls.attendancePct || 0) >= 60
                            ? 'bg-yellow-500'
                            : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(cls.attendancePct || 0, 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Faculty: {cls.facultyName}</span>
                      <span>{cls.totalStudents} students</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-8">No class comparison data available</p>
              )}
            </div>

            {/* Legend */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-4 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                  <span>Your Classes</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                  <span>≥80% Attendance</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                  <span>60-79% Attendance</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded mr-2"></div>
                  <span>&lt;60% Attendance</span>
                </div>
              </div>
            </div>
          </div>

          {/* Students Below Performance Threshold Pie Chart */}
          <div className="space-y-4">
            <div className="text-center">
              <h4 className="text-lg font-semibold text-gray-800 mb-2">Students Below Performance Threshold</h4>
              <p className="text-sm text-gray-600">Number of Students</p>
            </div>

            <div className="flex items-center justify-center">
              <div className="relative w-64 h-64">
                {/* Simple CSS-based pie chart */}
                <div className="w-full h-full rounded-full relative overflow-hidden">
                  {pieChartData.total > 0 ? (
                    <>
                      {/* Above threshold segment */}
                      <div
                        className="absolute inset-0 rounded-full"
                        style={{
                          background: `conic-gradient(
                            ${pieChartData.colors[0]} 0deg ${(pieChartData.data[0] / pieChartData.total) * 360}deg,
                            ${pieChartData.colors[1]} ${(pieChartData.data[0] / pieChartData.total) * 360}deg 360deg
                          )`
                        }}
                      ></div>

                      {/* Center circle for donut effect */}
                      <div className="absolute inset-8 bg-white rounded-full flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-gray-900">{pieChartData.total}</div>
                          <div className="text-xs text-gray-600">Total Students</div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-gray-500">No Data</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Performance Categories */}
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-500 rounded mr-3"></div>
                  <span className="font-medium text-green-800">Above {performanceThreshold.threshold || 75}%</span>
                </div>
                <span className="font-bold text-green-800">{pieChartData.data[0]}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-red-500 rounded mr-3"></div>
                  <span className="font-medium text-red-800">Below {performanceThreshold.threshold || 75}%</span>
                </div>
                <span className="font-bold text-red-800">{pieChartData.data[1]}</span>
              </div>
            </div>

            {/* Performance Summary */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-sm text-gray-600 mb-1">Performance Rate</div>
                <div className="text-2xl font-bold text-gray-900">
                  {pieChartData.total > 0
                    ? ((pieChartData.data[0] / pieChartData.total) * 100).toFixed(1)
                    : 0
                  }%
                </div>
                <div className="text-xs text-gray-500">
                  Students meeting attendance threshold
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FacultyAnalyticsPage;
