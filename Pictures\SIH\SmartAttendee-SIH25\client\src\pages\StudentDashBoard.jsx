import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useEffect, useState } from "react";
const StudentDashBoard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
 useEffect(() => {
    const fetchAnalytics = async () => {
      
  const authData = JSON.parse(localStorage.getItem("auth"));
const token = authData?.token;

      try {
        const res = await fetch(
          "https://smartattendee-sih25-backend.onrender.com/api/analytics/student",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const json = await res.json();
        setAnalytics(json);
      } catch (err) {
        console.error("Error fetching analytics:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchAnalytics();
     }, []);

  if (loading) return <p className="text-center">Loading...</p>;
  if (!analytics) return <p className="text-center text-red-500">No data found</p>;
const chartData = analytics?.weekChart?.raw?.map((d, i) => ({
    week: `Day ${i + 1}`,
    attendance: d.attendancePct ?? 0,
  }));

  return (
    <div className="space-y-12 p-6">
      {/* Welcome Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-3xl p-8 flex flex-col items-center shadow-lg">
        <h1 className="text-3xl font-bold">Welcome Back, Student!</h1>
        <p className="mt-2 text-sm md:text-base text-gray-200 text-center max-w-md">
          Here’s your performance summary and analytics for the semester.
        </p>
        <Button className="mt-6 bg-white text-indigo-700 hover:bg-gray-100 font-semibold px-6 py-2 rounded-full">
          Download PDF Report
        </Button>
      </section>

      {/* Attendance Overview */}
      <section>
        <h2 className="text-2xl font-bold text-center text-gray-800">Attendance Overview</h2>
        <p className="text-sm text-center text-gray-500 mt-1">
          Your attendance statistics in one view
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6">
          <Card className="shadow-lg rounded-2xl hover:shadow-xl transition">
            <CardContent className="p-6 text-center">
              <p className="text-3xl font-bold text-teal-600">  {analytics?.overall?.attendancePct ?? "--"}%</p>
              <p className="text-gray-500 mt-2">Current Attendance</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg rounded-2xl hover:shadow-xl transition">
            <CardContent className="p-6 text-center">
              <p className="text-3xl font-bold text-red-500"> {analytics?.overall?.totalAbsent ?? "--"}</p>
              <p className="text-gray-500 mt-2">Absences</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg rounded-2xl hover:shadow-xl transition">
            <CardContent className="p-6 text-center">
              <p className="text-3xl font-bold text-blue-500"> {analytics?.overall?.totalSessions ?? "--"}</p>
              <p className="text-gray-500 mt-2">Total Classes</p>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-center mt-6">
          <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:bg-indigo-700 font-semibold px-6 py-2 rounded-full">
            View Details
          </Button>
        </div>
      </section>

      {/* Attendance Trend */}
      <section>
        <h2 className="text-2xl font-bold text-center text-gray-800">Attendance Trend</h2>
        <p className="text-sm text-center text-gray-500 mt-1">
          Your attendance trend over the last month
        </p>

        <Card className="mt-6 shadow-xl rounded-2xl">
     <CardHeader> <p className="font-semibold text-gray-700 mb-4">Weekly Attendance</p>
</CardHeader>  
          <ResponsiveContainer width="100%" height={200}>
            <LineChart
              data={chartData}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="week" />
              <YAxis domain={[0, 100]} />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="attendance"
                stroke="#6366F1"
                strokeWidth={3}
                dot={{ r: 5, stroke: "#4ADE80", strokeWidth: 2, fill: "#fff" }}
                activeDot={{ r: 8 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>
      </section>
    </div>
  );
};

export default StudentDashBoard;
