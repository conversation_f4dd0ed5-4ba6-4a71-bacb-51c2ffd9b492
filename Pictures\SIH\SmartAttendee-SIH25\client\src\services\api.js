import axios from 'axios';

// Create axios instance with base configuration - Using local backend for development
const api = axios.create({
  baseURL: 'http://localhost:5001', // Local backend for development
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      console.log('🚫 401 Unauthorized - clearing token and redirecting to login');
      localStorage.removeItem('authToken');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userName');
      // Only redirect if not already on login page
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Auth API calls - Updated per documentation
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  profile: () => api.get('/auth/profile'), // Get user profile (protected route)
  logout: () => {
    // Clear token from localStorage and axios headers
    localStorage.removeItem('authToken');
    delete api.defaults.headers.common['Authorization'];
    return Promise.resolve();
  },
  // Faculty login using documented test credentials
  facultyLogin: () => {
    return api.post('/auth/login', {
      email: '<EMAIL>',
      password: 'faculty123'
    });
  }
};

// Attendance API calls - Updated to match backend routes
export const attendanceAPI = {
  generateQR: (data) => {
    // Generate QR session for class + subject attendance
    return api.post('/attendance/generate-qr', data);
  },
  markAttendance: (data) => api.post('/attendance/mark-attendance', data),
  getAttendanceRecords: (params) => api.get('/attendance/records', { params }),
};

// Analytics API calls
export const analyticsAPI = {
  getFacultyAnalytics: () => api.get('/api/analytics/faculty'),
  getStudentAnalytics: () => api.get('/api/analytics/student'),
  getAdminAnalytics: () => api.get('/api/analytics/admin'),
};

// Remove all mock data - use only real backend data

// Faculty specific API calls
export const facultyAPI = {
  generateQRCode: (data) => attendanceAPI.generateQR(data),
  getAnalytics: () => {
    // Use only real API call to backend - no fallback
    return analyticsAPI.getFacultyAnalytics();
  },
};

// Student specific API calls
export const studentAPI = {
  markAttendance: (data) => attendanceAPI.markAttendance(data),
  getAnalytics: () => analyticsAPI.getStudentAnalytics(),
  getAttendanceHistory: () => api.get('/student/attendance-history'),
};

// Utility functions
export const setAuthToken = (token) => {
  localStorage.setItem('authToken', token);
};

export const removeAuthToken = () => {
  localStorage.removeItem('authToken');
};

export const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

export default api;
