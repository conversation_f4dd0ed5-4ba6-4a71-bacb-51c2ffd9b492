// controllers/analyticsController.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

function startOfDay(d) {
  const x = new Date(d);
  x.setHours(0, 0, 0, 0);
  return x;
}
function endOfDay(d) {
  const x = new Date(d);
  x.setHours(23, 59, 59, 999);
  return x;
}

function daysAgo(n) {
  const d = new Date();
  d.setDate(d.getDate() - n);
  return d;
}

/**
 * Returns last N days as array of Date objects (oldest -> newest)
 */
function getLastNDays(n) {
  const days = [];
  const today = new Date();
  for (let i = n - 1; i >= 0; i--) {
    const d = new Date();
    d.setDate(today.getDate() - i);
    days.push(startOfDay(d));
  }
  return days;
}

const studentAnalytics = async (req, res) => {
  try {
    const userId = req.user.userId || req.user.id || req.user; // support different token shapes
    // Find the student profile and its class + class subjects
    const student = await prisma.student.findUnique({
      where: { userId: Number(userId) },
      include: {
        class: {
          include: {
            subjects: {
              include: {
                subject: true
              }
            }
          }
        }
      }
    });

    if (!student) {
      return res.status(404).json({ message: "Student not found" });
    }

    if (!student.class) {
      // Student has no class assigned yet
      return res.json({
        studentId: student.id,
        studentName: student.name,
        classId: null,
        className: null,
        overall: {
          totalSessions: 0,
          totalPresent: 0,
          totalAbsent: 0,
          attendancePct: 0
        },
        subjects: [],
        weekChart: { labels: [], values: [], raw: [] },
        prediction: { nextMonthPct: null, risk: false },
        suggestions: [],
        badges: [],
        lastUpdated: new Date().toISOString()
      });
    }

    const classId = student.class.id;
    const className = student.class.name;

    // collect subject ids linked to this class
    const classSubjects = (student.class.subjects || []).map(cs => ({
      id: cs.subject.id,
      name: cs.subject.name
    }));

    // --- 1) Per-subject totals (sessions available for that class+subject) and student's present count ---
    const subjectStats = await Promise.all(
      classSubjects.map(async (s) => {
        const [totalSessions, presentCount] = await Promise.all([
          prisma.qRSession.count({
            where: { classId: classId, subjectId: s.id }
          }),
          prisma.attendanceRecord.count({
            where: { studentId: student.id, classId: classId, subjectId: s.id }
          })
        ]);

        const absent = Math.max(0, totalSessions - presentCount);
        const pct = totalSessions > 0 ? (presentCount / totalSessions) * 100 : null;

        return {
          subjectId: s.id,
          subjectName: s.name,
          totalSessions,
          presentCount,
          absentCount: absent,
          attendancePct: pct === null ? null : Number(pct.toFixed(2))
        };
      })
    );

    // --- 2) Overall totals across all subjects for this class ---
    const totals = subjectStats.reduce(
      (acc, s) => {
        acc.totalSessions += s.totalSessions;
        acc.totalPresent += s.presentCount;
        acc.totalAbsent += s.absentCount;
        return acc;
      },
      { totalSessions: 0, totalPresent: 0, totalAbsent: 0 }
    );

    const overallPct = totals.totalSessions > 0 ? (totals.totalPresent / totals.totalSessions) * 100 : 0;

    // --- 3) Weekly trend: last 7 days (labels and values) ---
    const days = getLastNDays(7); // array of startOfDay dates
    const weekRaw = await Promise.all(
      days.map(async (dayStart) => {
        const dayEnd = endOfDay(dayStart);
        // get sessions for class across all subjects that were generated on that day
        const sessionsOnDay = await prisma.qRSession.findMany({
          where: {
            classId: classId,
            generatedAt: { gte: dayStart, lte: dayEnd }
          },
          select: { id: true }
        });

        const sessionIds = sessionsOnDay.map(s => s.id);
        let totalSessionsDay = sessionIds.length;
        let presentDay = 0;
        if (totalSessionsDay > 0) {
          presentDay = await prisma.attendanceRecord.count({
            where: {
              studentId: student.id,
              qrSessionId: { in: sessionIds }
            }
          });
        }

        const pct = totalSessionsDay > 0 ? (presentDay / totalSessionsDay) * 100 : null;
        return {
          date: dayStart.toISOString().slice(0, 10),
          totalSessions: totalSessionsDay,
          present: presentDay,
          attendancePct: pct === null ? null : Number(pct.toFixed(2))
        };
      })
    );

    const weekLabels = weekRaw.map(r => r.date);
    const weekValues = weekRaw.map(r => (r.attendancePct === null ? null : r.attendancePct));

    // --- 4) Simple prediction (basic moving-average of last 28 days) ---
    const last28Start = startOfDay(new Date(Date.now() - 27 * 24 * 60 * 60 * 1000)); // 28 days window
    const last28Sessions = await prisma.qRSession.findMany({
      where: { classId: classId, generatedAt: { gte: last28Start } },
      select: { id: true }
    });
    const last28SessionIds = last28Sessions.map(s => s.id);
    let predictedPct = null;
    if (last28SessionIds.length > 0) {
      const presentLast28 = await prisma.attendanceRecord.count({
        where: { studentId: student.id, qrSessionId: { in: last28SessionIds } }
      });
      predictedPct = Number(((presentLast28 / last28SessionIds.length) * 100).toFixed(2));
    }

    // Risk flag and suggestions
    const RISK_THRESHOLD = 75; // percent
    const risk = overallPct < RISK_THRESHOLD;
    // find subjects below threshold
    const subjectsNeedingAttention = subjectStats.filter(s => s.attendancePct !== null && s.attendancePct < RISK_THRESHOLD);

    const suggestions = [];
    if (subjectsNeedingAttention.length > 0) {
      for (const s of subjectsNeedingAttention) {
        suggestions.push(`Improve attendance in ${s.subjectName}: ${s.attendancePct}% present (${s.absentCount} missed).`);
      }
    } else if (overallPct >= 95) {
      suggestions.push('Great job — your attendance is excellent. Keep the streak!');
    } else {
      suggestions.push('Maintain consistency — try to attend classes regularly.');
    }

    // Badges (simple examples)
    const badges = [];
    // Perfect week badge if attendancePct all days where sessions exist = 100
    const weekHasSessions = weekRaw.some(r => r.totalSessions > 0);
    const perfectWeek = weekHasSessions && weekRaw.every(r => r.attendancePct === null || r.attendancePct === 100);
    if (perfectWeek) badges.push('Perfect Week');

    if (overallPct >= 95) badges.push('Attendance Star');
    if (overallPct >= 80 && overallPct < 95) badges.push('Consistent Attendee');

    // Build final payload
    const payload = {
      studentId: student.id,
      studentName: student.name,
      classId,
      className,
      overall: {
        totalSessions: totals.totalSessions,
        totalPresent: totals.totalPresent,
        totalAbsent: totals.totalAbsent,
        attendancePct: Number(overallPct.toFixed(2))
      },
      subjects: subjectStats,
      weekChart: {
        labels: weekLabels,            // e.g. ['2025-09-01','2025-09-02',...]
        values: weekValues,            // e.g. [80, null, 50, ...] — null means no sessions that day
        raw: weekRaw                   // detailed rows per day
      },
      prediction: {
        nextMonthPct: predictedPct,    // may be null if no data
        risk,
        threshold: RISK_THRESHOLD
      },
      suggestions,
      badges,
      lastUpdated: new Date().toISOString()
    };

    return res.json(payload);

  } catch (error) {
    console.error("Error fetching student analytics:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};


const facultyAnalytics = async (req, res) => {
  try {
    const tokenUser = req.user || {};
    const userId = tokenUser.userId || tokenUser.id || tokenUser; // support variations

    // Fetch faculty and the classes they manage
    const faculty = await prisma.faculty.findUnique({
      where: { userId: Number(userId) },
      include: {
        classes: {
          include: {
            students: true,
            subjects: {
              include: { subject: true } // ClassSubject -> subject
            }
          }
        }
      }
    });

    if (!faculty) {
      return res.status(404).json({ message: "Faculty not found" });
    }

    const FACULTY_ID = faculty.id;
    const now = new Date();
    const last7Start = startOfDay(daysAgo(6)); // last 7 days inclusive (day 0..6)
    const last30Start = startOfDay(daysAgo(29)); // last 30 days

    // Config
    const RISK_THRESHOLD = 75; // percent below which student is considered at-risk

    // We'll assemble per-class summaries
    const classSummaries = await Promise.all(faculty.classes.map(async (cls) => {
      const classId = cls.id;
      const totalStudents = cls.students.length;

      // Build subject list for this class (from ClassSubject join)
      const subjects = (cls.subjects || []).map(cs => ({ id: cs.subject.id, name: cs.subject.name }));

      // For each subject compute sessions & present counts
      const subjectStats = await Promise.all(subjects.map(async (s) => {
        const totalSessions = await prisma.qRSession.count({
          where: { classId: classId, subjectId: s.id }
        });

        const presentCount = await prisma.attendanceRecord.count({
          where: { classId: classId, subjectId: s.id }
        });

        // attendancePct defined as: presentCount / (totalSessions * totalStudents) * 100
        // (i.e., proportion of possible seats occupied across sessions). If totalSessions = 0 -> null
        const possibleSeats = totalSessions * Math.max(1, totalStudents);
        const attendancePct = totalSessions > 0 ? (presentCount / possibleSeats) * 100 : null;

        return {
          subjectId: s.id,
          subjectName: s.name,
          totalSessions,
          presentCount,
          possibleSeats,
          attendancePct: attendancePct === null ? null : Number(attendancePct.toFixed(2))
        };
      }));

      // Aggregate overall for this class across subjects
      const classTotals = subjectStats.reduce((acc, st) => {
        acc.totalSessions += st.totalSessions;
        acc.presentCount += st.presentCount;
        acc.possibleSeats += st.possibleSeats;
        return acc;
      }, { totalSessions: 0, presentCount: 0, possibleSeats: 0 });

      const overallAttendancePct = classTotals.totalSessions > 0 ? (classTotals.presentCount / Math.max(1, classTotals.possibleSeats)) * 100 : null;

      // Last-week trend for class (by day): compute sessions on each day and presents for that day
      const last7days = [];
      for (let i = 6; i >= 0; i--) {
        const d = startOfDay(daysAgo(i));
        const dayStart = d;
        const dayEnd = endOfDay(d);

        const sessions = await prisma.qRSession.findMany({
          where: { classId: classId, generatedAt: { gte: dayStart, lte: dayEnd } },
          select: { id: true, subjectId: true }
        });

        const sessionIds = sessions.map(s => s.id);
        const sessionsCount = sessionIds.length;

        let presentCount = 0;
        if (sessionIds.length > 0) {
          presentCount = await prisma.attendanceRecord.count({
            where: { classId: classId, qrSessionId: { in: sessionIds } }
          });
        }

        // attendancePct for that day (overall across subjects)
        const dayPossibleSeats = sessionsCount * Math.max(1, totalStudents);
        const dayPct = sessionsCount > 0 ? (presentCount / dayPossibleSeats) * 100 : null;

        last7days.push({
          date: dayStart.toISOString().slice(0, 10),
          sessionsCount,
          presentCount,
          attendancePct: dayPct === null ? null : Number(dayPct.toFixed(2))
        });
      }

      // Last-month aggregate (weekly buckets) - 4 weeks
      const last30Weeks = []; // array of 4 week aggregates
      for (let w = 0; w < 4; w++) {
        const start = startOfDay(daysAgo(29 - (w * 7)));
        const end = endOfDay(daysAgo(29 - (w * 7) + 6)); // 7-day window
        const sessions = await prisma.qRSession.findMany({
          where: { classId: classId, generatedAt: { gte: start, lte: end } },
          select: { id: true }
        });
        const sessionIds = sessions.map(s => s.id);
        const sessionsCount = sessionIds.length;
        let presentCount = 0;
        if (sessionIds.length > 0) {
          presentCount = await prisma.attendanceRecord.count({
            where: { classId: classId, qrSessionId: { in: sessionIds } }
          });
        }
        const possibleSeats = sessionsCount * Math.max(1, totalStudents);
        const pct = sessionsCount > 0 ? (presentCount / possibleSeats) * 100 : null;
        last30Weeks.push({
          weekIndex: w + 1,
          from: start.toISOString().slice(0, 10),
          to: end.toISOString().slice(0, 10),
          sessionsCount,
          presentCount,
          attendancePct: pct === null ? null : Number(pct.toFixed(2))
        });
      }

      // At-risk students for this class: students with subject-wise or overall attendance below threshold
      const studentAtRisk = [];
      for (const stu of cls.students) {
        // compute student's overall attendance for this class (across class subjects)
        // count student's present marks across sessions for this class
        const studentPresent = await prisma.attendanceRecord.count({
          where: { classId: classId, studentId: stu.id }
        });

        // total sessions for this class (sum of subject sessions)
        const totalSessionCountForClass = classTotals.totalSessions;
        const possibleStudentSeats = Math.max(1, totalSessionCountForClass);
        const studentPct = totalSessionCountForClass > 0 ? (studentPresent / possibleStudentSeats) * 100 : null;

        // also compute per-subject weak subjects for this student
        const weakSubjects = [];
        for (const st of subjects) {
          const subjSessions = await prisma.qRSession.count({ where: { classId: classId, subjectId: st.id } });
          if (subjSessions === 0) continue;
          const stuPresentSubject = await prisma.attendanceRecord.count({ where: { classId: classId, subjectId: st.id, studentId: stu.id } });
          const pctSub = (stuPresentSubject / Math.max(1, subjSessions)) * 100;
          if (pctSub < RISK_THRESHOLD) {
            weakSubjects.push({ subjectId: st.id, subjectName: st.name, pct: Number(pctSub.toFixed(2)) });
          }
        }

        if ((studentPct !== null && studentPct < RISK_THRESHOLD) || weakSubjects.length > 0) {
          studentAtRisk.push({
            studentId: stu.id,
            studentName: stu.name,
            enrollmentNo: stu.enrollmentNo || null,
            overallPct: studentPct === null ? null : Number(studentPct.toFixed(2)),
            weakSubjects
          });
        }
      }

      return {
        classId,
        className: cls.name,
        totalStudents,
        subjects: subjectStats,
        overall: {
          totalSessions: classTotals.totalSessions,
          presentCount: classTotals.presentCount,
          possibleSeats: classTotals.possibleSeats,
          attendancePct: classTotals.totalSessions > 0 ? Number(overallAttendancePct.toFixed(2)) : null
        },
        last7days,
        last30Weeks,
        atRiskStudents: studentAtRisk
      };
    }));

    // Cross-class / faculty-level analytics for charts
    // Bar chart: avg attendance % per subject across the faculty's classes
    // Collect distinct subject ids across all classes taught by this faculty
    const subjectMap = new Map();
    faculty.classes.forEach(c => {
      (c.subjects || []).forEach(cs => {
        const sid = cs.subject.id;
        subjectMap.set(sid, cs.subject.name);
      });
    });
    const subjectList = Array.from(subjectMap.entries()).map(([id, name]) => ({ id, name }));

    // For each subject compute average attendancePct across classes taught by faculty
    const subjectBarData = await Promise.all(subjectList.map(async (s) => {
      // gather classes that have this subject (and are taught by this faculty)
      const classesWithSubject = faculty.classes.filter(c => (c.subjects || []).some(cs => cs.subject.id === s.id));
      let sumPct = 0;
      let count = 0;
      for (const cls of classesWithSubject) {
        // compute presentCount and possibleSeats for cls & subject
        const totalSessions = await prisma.qRSession.count({ where: { classId: cls.id, subjectId: s.id } });
        const presentCount = await prisma.attendanceRecord.count({ where: { classId: cls.id, subjectId: s.id } });
        const possibleSeats = totalSessions * Math.max(1, cls.students.length);
        if (totalSessions > 0) {
          const pct = (presentCount / Math.max(1, possibleSeats)) * 100;
          sumPct += pct;
          count++;
        }
      }
      const avgPct = count > 0 ? Number((sumPct / count).toFixed(2)) : null;
      return { subjectId: s.id, subjectName: s.name, avgAttendancePct: avgPct };
    }));

    // Pie charts: for each subject, distribution of attendance (present) across classes (labels=classNames, data=presentCounts)
    const pieData = {};
    for (const s of subjectList) {
      const classesForSubject = faculty.classes.filter(c => (c.subjects || []).some(cs => cs.subject.id === s.id));
      const labels = [];
      const data = [];
      for (const cls of classesForSubject) {
        const presentCount = await prisma.attendanceRecord.count({ where: { classId: cls.id, subjectId: s.id }});
        labels.push(cls.name);
        data.push(presentCount);
      }
      pieData[s.id] = {
        subjectId: s.id,
        subjectName: s.name,
        labels,
        data
      };
    }

    //  metrics 
    // Engagement score per class: weighted combination of attendancePct + recency (last week)
    const engagement = classSummaries.map(cs => {
      const recent = cs.last7days || [];
      const recentAvg = recent.length > 0 ? (recent.reduce((a, r) => a + (r.attendancePct || 0), 0) / recent.filter(r => r.attendancePct !== null).length) : null;
      const overallPct = cs.overall.attendancePct || 0;
      // simple formula (50% overall, 50% recent if available)
      const score = recentAvg !== null ? (0.5 * overallPct + 0.5 * recentAvg) : overallPct;
      return { classId: cs.classId, className: cs.className, engagementScore: score === null ? null : Number(score.toFixed(2)) };
    });

    // Faculty-level suggestions
    const suggestions = [];
    // example: list top 3 classes with lowest engagement
    const sortedByEngagement = engagement.filter(e => e.engagementScore !== null).sort((a,b) => a.engagementScore - b.engagementScore);
    if (sortedByEngagement.length > 0) {
      const low = sortedByEngagement.slice(0, Math.min(3, sortedByEngagement.length));
      for (const l of low) {
        suggestions.push(`Class ${l.className} has low engagement (${l.engagementScore}%). Consider targeted interventions or attendance drives.`);
      }
    }

    // Add cross-class comparison data for faculty
    // Get all classes in the system for comparison
    const allClasses = await prisma.class.findMany({
      include: {
        students: true,
        faculty: true,
        subjects: {
          include: { subject: true }
        }
      }
    });

    // Build class comparison data (all classes vs faculty's classes)
    const classComparisonData = await Promise.all(allClasses.map(async (cls) => {
      const classStudents = cls.students.length;
      const totalSessions = await prisma.qRSession.count({ where: { classId: cls.id } });
      const presentCount = await prisma.attendanceRecord.count({ where: { classId: cls.id } });
      const possibleSeats = totalSessions * Math.max(1, classStudents);
      const attendancePct = totalSessions > 0 ? Number(((presentCount / Math.max(1, possibleSeats)) * 100).toFixed(2)) : 0;

      return {
        classId: cls.id,
        className: cls.name,
        facultyName: cls.faculty ? cls.faculty.name : 'Unassigned',
        attendancePct,
        totalStudents: classStudents,
        isOwnClass: faculty.classes.some(ownClass => ownClass.id === cls.id)
      };
    }));

    // Calculate performance threshold data for pie chart
    const allStudents = await prisma.student.findMany({
      where: { classId: { not: null } },
      include: { class: true }
    });

    let studentsAboveThreshold = 0;
    let studentsBelowThreshold = 0;

    for (const student of allStudents) {
      const presentCount = await prisma.attendanceRecord.count({
        where: { studentId: student.id, classId: student.classId }
      });
      const totalSessions = await prisma.qRSession.count({
        where: { classId: student.classId }
      });

      if (totalSessions > 0) {
        const studentPct = (presentCount / totalSessions) * 100;
        if (studentPct >= RISK_THRESHOLD) {
          studentsAboveThreshold++;
        } else {
          studentsBelowThreshold++;
        }
      }
    }

    const performanceThresholdData = {
      aboveThreshold: studentsAboveThreshold,
      belowThreshold: studentsBelowThreshold,
      threshold: RISK_THRESHOLD,
      total: studentsAboveThreshold + studentsBelowThreshold
    };

    // Build final response payload
    const payload = {
      facultyId: faculty.id,
      facultyName: faculty.name,
      generatedAt: now.toISOString(),
      config: {
        riskThreshold: RISK_THRESHOLD
      },
      classes: classSummaries,      // detailed per-class analytics
      subjectBarData,               // array for bar chart (per-subject avg attendance)
      subjectPieData: pieData,      // per-subject pie chart data keyed by subjectId
      engagement,                   // engagement scores per class
      suggestions,
      classComparison: classComparisonData,  // cross-class comparison data
      performanceThreshold: performanceThresholdData  // data for performance threshold pie chart
    };

    return res.json(payload);

  } catch (err) {
    console.error("Error in facultyAnalytics:", err);
    return res.status(500).json({ message: "Internal server error" });
  }
};

const adminAnalytics = async (req, res) => {
  try {
    const now = new Date();
    const last7Start = startOfDay(daysAgo(6));
    const last30Start = startOfDay(daysAgo(29));
    const RISK_THRESHOLD = 75; // config: percent

    // Fetch classes with minimal joins (students, faculty, ClassSubject->subject)
    const classes = await prisma.class.findMany({
      include: {
        students: true,
        faculty: true,
        subjects: {
          include: { subject: true } // class.subjects -> subject
        }
      }
    });

    // Precompute per-class aggregates
    const classSummaries = await Promise.all(classes.map(async (cls) => {
      const classId = cls.id;
      const totalStudents = cls.students.length;

      // subjects for this class
      const subjects = (cls.subjects || []).map(cs => ({ id: cs.subject.id, name: cs.subject.name }));

      // compute per-subject stats (totalSessions, presentCount, attendancePct)
      const subjectStats = await Promise.all(subjects.map(async (s) => {
        const totalSessions = await prisma.qRSession.count({ where: { classId, subjectId: s.id }});
        const presentCount = await prisma.attendanceRecord.count({ where: { classId, subjectId: s.id }});
        const possibleSeats = totalSessions * Math.max(1, totalStudents);
        const attendancePct = totalSessions > 0 ? (presentCount / Math.max(1, possibleSeats)) * 100 : null;
        return {
          subjectId: s.id,
          subjectName: s.name,
          totalSessions,
          presentCount,
          possibleSeats,
          attendancePct: attendancePct === null ? null : Number(attendancePct.toFixed(2))
        };
      }));

      // class totals aggregated across subjects
      const classTotals = subjectStats.reduce((acc, st) => {
        acc.totalSessions += st.totalSessions;
        acc.presentCount += st.presentCount;
        acc.possibleSeats += st.possibleSeats;
        return acc;
      }, { totalSessions: 0, presentCount: 0, possibleSeats: 0 });

      const overallPct = classTotals.totalSessions > 0 ? (classTotals.presentCount / Math.max(1, classTotals.possibleSeats)) * 100 : null;

      // last 7 days trend for this class
      const last7days = [];
      for (let i = 6; i >= 0; i--) {
        const dayStart = startOfDay(daysAgo(i));
        const dayEnd = endOfDay(daysAgo(i));
        const sessions = await prisma.qRSession.findMany({
          where: { classId, generatedAt: { gte: dayStart, lte: dayEnd } },
          select: { id: true }
        });
        const sessionIds = sessions.map(s => s.id);
        const sessionCount = sessionIds.length;
        let presentCount = 0;
        if (sessionCount > 0) {
          presentCount = await prisma.attendanceRecord.count({
            where: { classId, qrSessionId: { in: sessionIds } }
          });
        }
        const dayPossibleSeats = sessionCount * Math.max(1, totalStudents);
        const dayPct = sessionCount > 0 ? (presentCount / Math.max(1, dayPossibleSeats)) * 100 : null;
        last7days.push({
          date: dayStart.toISOString().slice(0,10),
          sessionsCount: sessionCount,
          presentCount,
          attendancePct: dayPct === null ? null : Number(dayPct.toFixed(2))
        });
      }

      // last 30 days aggregated in 4 weekly buckets
      const last30Weeks = [];
      for (let w = 0; w < 4; w++) {
        const start = startOfDay(daysAgo(29 - (w * 7)));
        const end = endOfDay(daysAgo(29 - (w * 7) + 6));
        const sessions = await prisma.qRSession.findMany({
          where: { classId, generatedAt: { gte: start, lte: end } },
          select: { id: true }
        });
        const sessionIds = sessions.map(s => s.id);
        const sessionCount = sessionIds.length;
        let presentCount = 0;
        if (sessionCount > 0) {
          presentCount = await prisma.attendanceRecord.count({ where: { classId, qrSessionId: { in: sessionIds } } });
        }
        const possibleSeats = sessionCount * Math.max(1, totalStudents);
        const pct = sessionCount > 0 ? (presentCount / Math.max(1, possibleSeats)) * 100 : null;
        last30Weeks.push({
          weekIndex: w+1,
          from: start.toISOString().slice(0,10),
          to: end.toISOString().slice(0,10),
          sessionsCount: sessionCount,
          presentCount,
          attendancePct: pct === null ? null : Number(pct.toFixed(2))
        });
      }

      return {
        classId,
        className: cls.name,
        facultyName: cls.faculty ? cls.faculty.name : null,
        totalStudents,
        subjects: subjectStats,
        overall: {
          totalSessions: classTotals.totalSessions,
          presentCount: classTotals.presentCount,
          possibleSeats: classTotals.possibleSeats,
          attendancePct: classTotals.totalSessions > 0 ? Number(overallPct.toFixed(2)) : null
        },
        last7days,
        last30Weeks
      };
    }));

    // 3) Subject-level summary across all classes (distinct subjects in DB)
    const allSubjects = await prisma.subject.findMany({
      include: {
        classes: { include: { class: true } } // classes referencing this subject
      }
    });

    const subjectSummary = await Promise.all(allSubjects.map(async (s) => {
      // for this subject, gather classes where it exists
      const linkedClasses = (s.classes || []).map(cs => cs.class);
      let totalSessions = 0;
      let presentCount = 0;
      for (const cls of linkedClasses) {
        const cntSessions = await prisma.qRSession.count({ where: { classId: cls.id, subjectId: s.id } });
        const cntPresent = await prisma.attendanceRecord.count({ where: { classId: cls.id, subjectId: s.id } });
        totalSessions += cntSessions;
        presentCount += cntPresent;
      }
      // possible seats = totalSessions * average class size? For simplicity, use totalSessions * 1.. but better: sum (sessions * classStudents)
      // We'll compute possibleSeats as sum over classes (sessions * students)
      let possibleSeats = 0;
      for (const cls of linkedClasses) {
        const classStudentsCount = (await prisma.student.count({ where: { classId: cls.id } })) || 0;
        const cntSessions = await prisma.qRSession.count({ where: { classId: cls.id, subjectId: s.id } });
        possibleSeats += cntSessions * Math.max(1, classStudentsCount);
      }
      const avgAttendancePct = totalSessions > 0 ? (presentCount / Math.max(1, possibleSeats)) * 100 : null;

      return {
        subjectId: s.id,
        subjectName: s.name,
        totalSessions,
        presentCount,
        possibleSeats,
        avgAttendancePct: avgAttendancePct === null ? null : Number(avgAttendancePct.toFixed(2))
      };
    }));

    // 4) Bar chart: class comparison (className vs attendancePct)
    const classComparisonBar = classSummaries.map(cs => ({
      classId: cs.classId,
      className: cs.className,
      attendancePct: cs.overall.attendancePct // can be null
    }));

    // 5) Faculty-level comparison (avg attendance across their classes)
    const faculties = await prisma.faculty.findMany({
      include: { classes: { include: { students: true, subjects: { include: { subject: true } } } } }
    });
    const facultyComparison = await Promise.all(faculties.map(async (f) => {
      // compute avg attendance across f.classes
      let sumPct = 0;
      let count = 0;
      for (const cls of f.classes) {
        // compute class overall attendance quickly by counting sessions and present
        const classStudents = cls.students.length;
        // gather subject list
        const subjectsForClass = (cls.subjects || []).map(cs => cs.subject.id);
        // count sessions across all subjects for that class
        const totalSessions = await prisma.qRSession.count({ where: { classId: cls.id } });
        const presentCount = await prisma.attendanceRecord.count({ where: { classId: cls.id } });
        const possibleSeats = totalSessions * Math.max(1, classStudents);
        if (totalSessions > 0) {
          const pct = (presentCount / Math.max(1, possibleSeats)) * 100;
          sumPct += pct;
          count++;
        }
      }
      const avgPct = count > 0 ? Number((sumPct / count).toFixed(2)) : null;
      return { facultyId: f.id, facultyName: f.name, avgAttendancePct: avgPct };
    }));

    // 6) Pie data for each subject: distribution of presentCount across classes (labels = className, data = presentCount)
    const subjectPieData = {};
    for (const s of allSubjects) {
      const labels = [];
      const data = [];
      // find classes where s is linked
      const linked = (s.classes || []).map(cs => cs.class);
      for (const cls of linked) {
        const presentCount = await prisma.attendanceRecord.count({ where: { classId: cls.id, subjectId: s.id }});
        labels.push(cls.name);
        data.push(presentCount);
      }
      subjectPieData[s.id] = { subjectId: s.id, subjectName: s.name, labels, data };
    }

    // 7) At-risk students across school (scan students with classId not null)
    const students = await prisma.student.findMany({ where: { classId: { not: null } }, include: { class: true }});
    const atRiskStudents = [];
    for (const stu of students) {
      // compute student's present marks in their class
      const presentCount = await prisma.attendanceRecord.count({ where: { studentId: stu.id, classId: stu.classId }});
      // compute class total sessions (sum of sessions for class)
      const classSessionsForStudent = await prisma.qRSession.count({ where: { classId: stu.classId }});
      const possible = classSessionsForStudent;
      const overallPct = classSessionsForStudent > 0 ? (presentCount / Math.max(1, possible)) * 100 : null;

      // per-subject weak list
      const weakSubjects = [];
      const classSubjs = await prisma.classSubject.findMany({ where: { classId: stu.classId }, include: { subject: true }});
      for (const cs of classSubjs) {
        const subjSessions = await prisma.qRSession.count({ where: { classId: stu.classId, subjectId: cs.subjectId }});
        if (subjSessions === 0) continue;
        const stuPresentSub = await prisma.attendanceRecord.count({ where: { studentId: stu.id, classId: stu.classId, subjectId: cs.subjectId }});
        const pctSub = (stuPresentSub / Math.max(1, subjSessions)) * 100;
        if (pctSub < RISK_THRESHOLD) {
          weakSubjects.push({ subjectId: cs.subjectId, subjectName: cs.subject.name, pct: Number(pctSub.toFixed(2)) });
        }
      }

      if ((overallPct !== null && overallPct < RISK_THRESHOLD) || weakSubjects.length > 0) {
        atRiskStudents.push({
          studentId: stu.id,
          studentName: stu.name,
          classId: stu.classId,
          className: stu.class ? stu.class.name : null,
          overallPct: overallPct === null ? null : Number(overallPct.toFixed(2)),
          weakSubjects
        });
      }
    }

    // 8) Key aggregated stats for top tiles
    const totalClasses = classes.length;
    const totalStudents = await prisma.student.count();
    // total sessions across all classes
    const totalSessionsAll = await prisma.qRSession.count();
    // total present counts
    const totalPresentsAll = await prisma.attendanceRecord.count();
    const overallAttendancePct = totalSessionsAll > 0 ? (totalPresentsAll / (totalSessionsAll * Math.max(1, totalStudents))) * 100 : null;

    const keyStats = {
      totalClasses,
      totalStudents,
      totalSessions: totalSessionsAll,
      totalPresents: totalPresentsAll,
      averageAttendancePct: overallAttendancePct === null ? null : Number(overallAttendancePct.toFixed(2))
    };

    // suggestions: top 3 subjects with lowest avgAttendancePct (from subjectSummary)
    const lowSubjects = subjectSummary.filter(s => s.avgAttendancePct !== null).sort((a,b) => a.avgAttendancePct - b.avgAttendancePct).slice(0,3);
    const suggestions = lowSubjects.map(s => `Subject ${s.subjectName} has low average attendance (${s.avgAttendancePct}%). Consider subject-specific interventions.`);

    // 9) Build final payload
    const payload = {
      generatedAt: now.toISOString(),
      config: { riskThreshold: RISK_THRESHOLD },
      keyStats,
      classes: classSummaries,
      subjectSummary,
      classComparisonBar,   // array of {classId, className, attendancePct}
      facultyComparison,    // array of {facultyId, facultyName, avgAttendancePct}
      subjectPieData,       // keyed by subjectId { labels, data }
      atRiskStudents,
      suggestions
    };

    return res.json(payload);

  } catch (err) {
    console.error('adminAnalytics error:', err);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  studentAnalytics,
  facultyAnalytics,
  adminAnalytics
};
