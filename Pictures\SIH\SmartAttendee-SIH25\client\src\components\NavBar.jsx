import React, { useState } from 'react';
import { Search, Users } from 'lucide-react';
import { useLocation } from 'react-router-dom';

const NavBar = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const location = useLocation();

  // Determine the page title based on current route
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/faculty':
        return 'Faculty Dashboard';
      case '/student':
        return 'Student Dashboard';
      default:
        return 'Dashboard';
    }
  };

  const getNavigationItems = () => {
    if (location.pathname === '/faculty') {
      return [
        { label: 'Dashboard', href: '/faculty', active: true },
        { label: 'My Classes', href: '/faculty/classes', active: false },
        { label: 'Attendance', href: '/faculty/attendance', active: false },
        { label: 'Analytics', href: '/faculty/analytics', active: false },
        { label: 'Settings', href: '/faculty/settings', active: false }
      ];
    }
    return [];
  };

  return (
    <header className="bg-white shadow-sm border-b h-16">
      <div className="flex items-center justify-between px-6 py-4 h-full">
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">{getPageTitle()}</h2>
          </div>

          <nav className="flex space-x-6">
            {getNavigationItems().map((item, index) => (
              <a
                key={index}
                href={item.href}
                className={`font-medium transition-colors ${
                  item.active
                    ? 'text-blue-600'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                {item.label}
              </a>
            ))}
          </nav>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search in site"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default NavBar;