// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Role {
  id    Int    @id @default(autoincrement())
  name  String @unique

  users User[]
}

model User {
  id           Int      @id @default(autoincrement())
  email        String   @unique
  passwordHash String
  roleId       Int
  createdAt    DateTime @default(now())

  role   Role   @relation(fields: [roleId], references: [id])
  student Student?
  faculty Faculty?
  parent Parent?
}


model Student {
  id               Int      @id @default(autoincrement())
  userId           Int      @unique
  name             String
  classId          Int?
  enrollmentNo     String?  @unique

  user             User   @relation(fields: [userId], references: [id])
  class            Class? @relation(fields: [classId], references: [id])
  parents          StudentParent[]
  attendanceRecords AttendanceRecord[]

  @@index([classId])
}

model Faculty {
  id         Int      @id @default(autoincrement())
  userId     Int      @unique
  name       String
  department String?

  user      User    @relation(fields: [userId], references: [id])
  classes   Class[]
  qrSessions QRSession[]
}

model Parent {
  id     Int      @id @default(autoincrement())
  userId Int      @unique
  name   String
  phone  String

  user     User     @relation(fields: [userId], references: [id])
  students StudentParent[]
}

model StudentParent {
  studentId Int
  parentId  Int

  student Student @relation(fields: [studentId], references: [id])
  parent  Parent  @relation(fields: [parentId], references: [id])

  @@id([studentId, parentId])
}

model Class {
  id        Int      @id @default(autoincrement())
  name      String
  facultyId Int

  faculty           Faculty @relation(fields: [facultyId], references: [id])
  students          Student[]
  qrSessions        QRSession[]
  attendanceRecords AttendanceRecord[]
  subjects          ClassSubject[]  // join entries

  @@index([facultyId])
}

model Subject {
  id     Int    @id @default(autoincrement())
  name   String @unique

  classes ClassSubject[]

  QRSession QRSession[]

  AttendanceRecord AttendanceRecord[]
}

model ClassSubject {
  classId   Int
  subjectId Int

  class   Class   @relation(fields: [classId], references: [id])
  subject Subject @relation(fields: [subjectId], references: [id])

  @@id([classId, subjectId])
  @@index([subjectId])
}

model AttendanceRecord {
  id          Int      @id @default(autoincrement())
  studentId   Int
  classId     Int
  subjectId   Int
  datetime    DateTime @default(now())
  status      String
  qrSessionId Int

  student   Student   @relation(fields: [studentId], references: [id])
  class     Class     @relation(fields: [classId], references: [id])
  subject   Subject   @relation(fields: [subjectId], references: [id])
  qrSession QRSession @relation(fields: [qrSessionId], references: [id])

  // Prevent duplicate attendance for same student and qrSession
  @@unique([studentId, qrSessionId], name: "studentId_qrSessionId")

  @@index([studentId])
  @@index([classId])
  @@index([subjectId])
}

model QRSession {
  id          Int      @id @default(autoincrement())
  facultyId   Int
  classId     Int
  subjectId   Int?
  generatedAt DateTime @default(now())
  geoLat      Float
  geoLng      Float
  isActive    Boolean  @default(true)

  faculty Faculty @relation(fields: [facultyId], references: [id])
  class   Class   @relation(fields: [classId], references: [id])
  subject Subject? @relation(fields: [subjectId], references: [id])
  attendanceRecords AttendanceRecord[]

  @@index([facultyId])
  @@index([classId])
  @@index([subjectId])
}