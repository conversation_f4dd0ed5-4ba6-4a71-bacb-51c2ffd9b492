import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

const AdminDashboard = () => {
  const navigate = useNavigate();
  return (
    <div className="space-y-16 p-8 bg-gray-50 min-h-screen">
      {/* Welcome Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-3xl p-10 flex flex-col items-center shadow-2xl max-w-5xl mx-auto">
        <h1 className="text-4xl font-extrabold tracking-tight drop-shadow-md text-center">
          Welcome to the Smart Attendance System
        </h1>
        <p className="mt-3 text-base md:text-lg text-gray-200 text-center max-w-2xl">
          Effortlessly manage user attendance, monitor analytics, and assign
          classes all in one place.
        </p>
        <div className="flex space-x-4 mt-8">
          <Button className="bg-white text-indigo-700 hover:bg-gray-100 active:bg-indigo-700 active:text-white font-semibold px-6 py-2 rounded-full shadow-md transition">
            Help
          </Button>
          <Button
            onClick={() => navigate("/AdminAnalytics")}
            className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:opacity-90 active:bg-white active:text-indigo-700 font-semibold px-6 py-2 rounded-full shadow-md transition"
          >
            Get Started
          </Button>
        </div>
      </section>

      {/* Analytics Overview */}
      <section className="max-w-5xl mx-auto text-center">
        <h2 className="text-3xl font-bold text-gray-800">Analytics Overview</h2>
        <p className="text-base text-gray-500 mt-2">
          A snapshot of your platform’s performance
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 mt-8">
          <Card className="shadow-xl rounded-3xl hover:scale-105 transform transition-all">
            <CardContent className="p-8 text-center">
              <p className="text-4xl font-bold text-indigo-600">150</p>
              <p className="text-gray-600 mt-2 font-medium">Total Users</p>
              <span className="text-green-600 text-sm font-semibold">
                +5% this month
              </span>
            </CardContent>
          </Card>

          <Card className="shadow-xl rounded-3xl hover:scale-105 transform transition-all">
            <CardContent className="p-8 text-center">
              <p className="text-4xl font-bold text-purple-600">30</p>
              <p className="text-gray-600 mt-2 font-medium">Total Classes</p>
              <span className="text-green-600 text-sm font-semibold">
                +10% this term
              </span>
            </CardContent>
          </Card>

          <Card className="shadow-xl rounded-3xl hover:scale-105 transform transition-all">
            <CardContent className="p-8 text-center">
              <p className="text-4xl font-bold text-teal-600">92%</p>
              <p className="text-gray-600 mt-2 font-medium">Attendance Rate</p>
              <span className="text-red-600 text-sm font-semibold">
                -1% from last week
              </span>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="max-w-5xl mx-auto text-center">
        <h2 className="text-3xl font-bold text-gray-800">Quick Actions</h2>
        <p className="text-base text-gray-500 mt-2">
          Speed up your workflows with these shortcuts
        </p>

        <div className="flex justify-center space-x-4 mt-8">
          <Button className="bg-white text-indigo-700 hover:bg-gray-100 active:bg-indigo-700 active:text-white font-semibold px-6 py-2 rounded-full shadow-md transition">
            Assign Classes
          </Button>
          <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:opacity-90 active:bg-white active:text-indigo-700 px-6 py-2 rounded-full shadow-md transition">
            User Onboarding
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
          <Card className="shadow-lg rounded-2xl hover:shadow-xl transition">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-2 text-gray-800 text-lg">
                User Onboarding
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Quickly onboard new users and give them access to the platform.
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-lg rounded-2xl hover:shadow-xl transition">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-2 text-gray-800 text-lg">
                Class Assignment
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Easily assign classes and subjects to students and instructors.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Recent Activity */}
      <section className="max-w-5xl mx-auto text-center">
        <div className="mb-4">
          <h2 className="text-3xl font-bold text-gray-800">Recent Activity</h2>
          <p className="text-base text-gray-500 mt-1">
            Stay updated with the latest actions performed in the system
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
          <Card className="shadow-md rounded-2xl hover:shadow-lg transition">
            <CardContent className="p-5 flex items-center space-x-3">
              <span className="text-2xl">👤</span>
              <p className="text-gray-700 font-medium">
                User <span className="font-semibold">John Doe</span> onboarded
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-md rounded-2xl hover:shadow-lg transition">
            <CardContent className="p-5 flex items-center space-x-3">
              <span className="text-2xl">🛠️</span>
              <p className="text-gray-700 font-medium">
                Class <span className="font-semibold">Math 101</span> assigned
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-md rounded-2xl hover:shadow-lg transition">
            <CardContent className="p-5 flex items-center space-x-3">
              <span className="text-2xl">📅</span>
              <p className="text-gray-700 font-medium">
                Attendance recorded for{" "}
                <span className="font-semibold">Science 202</span>
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-md rounded-2xl hover:shadow-lg transition">
            <CardContent className="p-5 flex items-center space-x-3">
              <span className="text-2xl">⚠️</span>
              <p className="text-gray-700 font-medium">
                User <span className="font-semibold">Jane Smith</span> marked
                absent
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="text-center text-gray-500 text-sm mt-16 border-t pt-6">
        © 2023 Smart Attendance System ·{" "}
        <a href="#" className="hover:underline">
          Privacy Policy
        </a>{" "}
        ·{" "}
        <a href="#" className="hover:underline">
          Terms of Service
        </a>
      </footer>
    </div>
  );
};

export default AdminDashboard;
