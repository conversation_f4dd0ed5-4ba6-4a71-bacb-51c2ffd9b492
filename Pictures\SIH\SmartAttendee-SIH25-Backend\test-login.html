<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h1>Test Login API</h1>
    <button onclick="testLogin()">Test Faculty Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing server health...');
                
                // Test server health first
                const healthResponse = await fetch('http://localhost:5000/');
                const healthData = await healthResponse.json();
                console.log('Server health:', healthData);
                
                // Test login
                console.log('Testing login...');
                const loginResponse = await fetch('http://localhost:5000/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'faculty123'
                    })
                });
                
                if (!loginResponse.ok) {
                    throw new Error(`HTTP error! status: ${loginResponse.status}`);
                }
                
                const loginData = await loginResponse.json();
                console.log('Login response:', loginData);
                
                resultDiv.innerHTML = `
                    <h3>✅ Success!</h3>
                    <p><strong>Server Health:</strong> ${healthData.message}</p>
                    <p><strong>Token:</strong> ${loginData.token ? 'Received' : 'Not received'}</p>
                    <p><strong>User:</strong> ${JSON.stringify(loginData.user)}</p>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Error!</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
