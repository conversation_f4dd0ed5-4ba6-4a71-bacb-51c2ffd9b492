{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "seed": "node prisma/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.15.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "pg": "^8.16.3", "twilio": "^5.9.0"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.15.0"}}