// import { useState } from 'react'
import './App.css'
import {BrowserRouter as Router, Route, Routes} from 'react-router-dom'
import MainLayout from './layout/MainLayout'
import StudentDashBoard from './pages/StudentDashBoard'
import FacultyDashboard from './pages/FacultyDashboard'
import FacultyAnalyticsPage from './pages/FacultyAnalytics'
import Register from './pages/Register'
import Welcome from './pages/Welcome'
import StudentAnalyticsPage from './pages/StudentAnalytics'
import Login from './pages/Login'
import AdminDashboard from './pages/AdminDashBoard'
import AdminAnalyticsOverview from './pages/AdminAnalyticsOverview'

function App() {
  return (
    <>
   <Router>
    <Routes>
      {/* Welcome and Auth routes without layout */}
      <Route path="/" element={<Welcome/>}></Route>
      <Route path="/login" element={<Login/>}></Route>
      <Route path="/register" element={<Register/>}></Route>
      {/* Test route */}
      <Route path="/test-faculty" element={<FacultyAnalyticsPage/>}></Route>
      {/* Direct faculty analytics route (temporary) */}
      <Route path="/faculty-analytics-direct" element={<FacultyAnalyticsPage/>}></Route>

      {/* Protected routes with layout */}
      <Route element={<MainLayout/>}>
        <Route path="/student" element={<StudentDashBoard/>}></Route>
        <Route path="/student/analytics" element={<StudentAnalyticsPage/>}></Route>
        <Route path="/faculty" element={<FacultyDashboard/>}></Route>
        {/* Faculty Analytics Route - Updated */}
        <Route path="/faculty/analytics" element={<FacultyAnalyticsPage/>}></Route>
        <Route path="/admin" element={<AdminDashboard/>}></Route>
        <Route path="/admin/analytics" element={<AdminAnalyticsOverview/>}></Route>
      </Route>
    </Routes>
   </Router>
    </>
  );
}

export default App;
