const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const twilio = require('twilio');
require('dotenv').config();

const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

const sendParentSummary = async (req, res) => {
  try {
    const { studentId } = req.body;

    // Find student and related attendance
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: {
        attendanceRecords: true,
        parents: {
          include: {
            parent: true
          }
        }
      }
    });

    if (!student) {
      return res.status(404).json({ message: "Student not found" });
    }

    const totalAttendance = student.attendanceRecords.length;
    const attendancePercentage = student.attendanceRecords.length > 0 ? 
      (student.attendanceRecords.length / 20 * 100).toFixed(2) : '0.00'; // Example denominator

    const messageBody = `Hello! Your child ${student.name} attended ${attendancePercentage}% of classes this month.`;

    // Send message to each parent
    for (const studentParent of student.parents) {
      const phone = studentParent.parent.phone;

      try {
        await client.messages.create({
          body: messageBody,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: phone
        });
      } catch (error) {
        console.error(`Failed to send message to ${phone}:`, error.message);
      }
    }

    res.json({ message: "Notifications sent successfully" });
  } catch (error) {
    console.error("Error sending parent summary:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

module.exports = {
  sendParentSummary
};
