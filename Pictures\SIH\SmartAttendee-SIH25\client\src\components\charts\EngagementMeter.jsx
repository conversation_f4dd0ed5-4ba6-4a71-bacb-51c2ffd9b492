import React from 'react';
import { TrendingUp } from 'lucide-react';

const EngagementMeter = ({ engagementData, title = "Class Engagement Scores" }) => {
  if (!engagementData || engagementData.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        No engagement data available
      </div>
    );
  }

  const getEngagementColor = (score) => {
    if (score >= 80) return 'bg-green-600';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getEngagementTextColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <TrendingUp className="w-5 h-5 text-green-600" />
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>
      
      {engagementData.map((eng) => (
        <div key={eng.classId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span className="font-medium text-gray-800">{eng.className}</span>
          <div className="flex items-center space-x-3">
            <div className="w-32 bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${getEngagementColor(eng.engagementScore || 0)}`}
                style={{ width: `${Math.min(100, eng.engagementScore || 0)}%` }}
              ></div>
            </div>
            <span className={`text-sm font-semibold w-12 ${getEngagementTextColor(eng.engagementScore || 0)}`}>
              {eng.engagementScore?.toFixed(1) || 0}%
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EngagementMeter;
