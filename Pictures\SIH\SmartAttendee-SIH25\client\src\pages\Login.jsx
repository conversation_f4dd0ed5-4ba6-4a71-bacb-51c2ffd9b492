import React, { useState } from "react";
import UserLogin from "./UserLogin";

export default function Login() {
  const [showUserLogin, setShowUserLogin] = useState(false);

  return (
    <>
      
     

     
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        {!showUserLogin ? (
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-800">
              Login To Your Account
            </h1>
            <h4 className="font-medium mt-2 text-gray-600">
              Please enter your credentials to continue
            </h4>
            <button
              className="rounded-lg bg-indigo-600 mt-6 text-white px-8 py-3 font-semibold shadow-md 
              transition-all duration-300 hover:bg-indigo-700 active:bg-white active:text-indigo-700"
              onClick={() => setShowUserLogin(true)}
            >
              Login
            </button>
          </div>
        ) : (
          <UserLogin />
        )}
      </div>
    </>
  );
}
