import Footer from "../components/Footer";
import NavBar from "../components/NavBar";
import SideBar from "../components/Sidebar";
import { Outlet } from "react-router-dom";

const MainLayout = () => {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 text-gray-900">
      {/* Navbar fixed at top */}
      <div className="fixed top-0 left-0 right-0 z-50">
        <NavBar />
      </div>

      <div className="flex flex-1 pt-16">
        {/* Sidebar fixed on left */}
        <aside className="w-64 fixed top-16 left-0 bottom-0 text-white overflow-y-auto">
          <SideBar />
        </aside>

        {/* Main content + footer */}
        <div className="flex flex-col flex-1 ml-64">
          <main className="flex-1 p-6 overflow-y-auto">
            <Outlet />
          </main>

          {/* Footer only spans content area */}
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
