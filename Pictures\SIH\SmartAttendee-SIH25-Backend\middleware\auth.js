const jwt = require('jsonwebtoken');
require('dotenv').config();

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer token

  if (!token) {
    return res.status(401).json({ message: 'Access token missing' });
  }

  // Allow mock tokens for development
  if (token.startsWith('mock-faculty-token')) {
    req.user = {
      id: 1,
      userId: 1,
      roleId: 2, // faculty role
      role: 'faculty'
    };
    return next();
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' });
    }
    req.user = user; // Attaching user info to request
    next();
  });
}

module.exports = authenticateToken;
