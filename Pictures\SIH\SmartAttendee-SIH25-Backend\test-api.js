const axios = require('axios');

async function testAPI() {
  try {
    console.log('Testing server health...');

    // Test server health first
    const healthResponse = await axios.get('http://localhost:5000/');
    console.log('Server health check:', healthResponse.data.message);

    console.log('\nTesting faculty login...');

    // Test faculty login
    const loginResponse = await axios.post('http://localhost:5000/auth/login', {
      email: '<EMAIL>',
      password: 'faculty123'
    });

    console.log('Login successful!');
    console.log('Token received:', loginResponse.data.token ? 'Yes' : 'No');
    console.log('User data:', loginResponse.data.user);

    const token = loginResponse.data.token;

    // Test faculty analytics
    console.log('\nTesting faculty analytics...');
    const analyticsResponse = await axios.get('http://localhost:5000/api/analytics/faculty', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Analytics API successful!');
    console.log('Faculty Name:', analyticsResponse.data.facultyName);
    console.log('Number of classes:', analyticsResponse.data.classes?.length || 0);
    console.log('Number of subjects:', analyticsResponse.data.subjectBarData?.length || 0);

    // Print first class details
    if (analyticsResponse.data.classes && analyticsResponse.data.classes.length > 0) {
      const firstClass = analyticsResponse.data.classes[0];
      console.log('\nFirst class details:');
      console.log('- Class Name:', firstClass.className);
      console.log('- Total Students:', firstClass.totalStudents);
      console.log('- Overall Attendance:', firstClass.overall?.attendancePct?.toFixed(1) + '%');
      console.log('- Number of subjects:', firstClass.subjects?.length || 0);
    }

    console.log('\n✅ All API tests passed!');

  } catch (error) {
    console.error('❌ API Test failed:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.message || error.message);
    if (error.code) {
      console.error('Error Code:', error.code);
    }
    if (error.response?.data) {
      console.error('Response Data:', error.response.data);
    }
  }
}

testAPI();
