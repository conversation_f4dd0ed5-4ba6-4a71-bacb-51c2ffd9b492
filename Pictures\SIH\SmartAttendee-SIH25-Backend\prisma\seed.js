// prisma/seed.js
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function upsertRole(name) {
  return prisma.role.upsert({
    where: { name },
    update: {},
    create: { name },
  });
}

async function main() {
  console.log('Seeding started...');

  // 1) Roles
  const roleNames = ['admin', 'faculty', 'student', 'parent'];
  for (const rn of roleNames) await upsertRole(rn);
  console.log('Roles upserted.');

  // fetch role ids once
  const roleAdmin = await prisma.role.findUnique({ where: { name: 'admin' }});
  const roleFaculty = await prisma.role.findUnique({ where: { name: 'faculty' }});
  const roleStudent = await prisma.role.findUnique({ where: { name: 'student' }});
  const roleParent = await prisma.role.findUnique({ where: { name: 'parent' }});

  // 2) Admin user
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123';
  const hashedAdmin = await bcrypt.hash(adminPassword, 10);

  await prisma.user.upsert({
    where: { email: adminEmail },
    update: { passwordHash: hashedAdmin, roleId: roleAdmin.id },
    create: { email: adminEmail, passwordHash: hashedAdmin, roleId: roleAdmin.id },
  });
  console.log('Admin user upserted.');

  // 3) Faculties (2)
  const facultyData = [
    { email: '<EMAIL>', name: 'Dr. Mehta', department: 'Computer Science', password: 'faculty123' },
    { email: '<EMAIL>', name: 'Prof. Sharma', department: 'Mathematics', password: 'faculty123' },
  ];

  const facultyProfiles = [];
  for (const f of facultyData) {
    const hashed = await bcrypt.hash(f.password, 10);
    const user = await prisma.user.upsert({
      where: { email: f.email },
      update: { passwordHash: hashed, roleId: roleFaculty.id },
      create: { email: f.email, passwordHash: hashed, roleId: roleFaculty.id },
    });

    const faculty = await prisma.faculty.upsert({
      where: { userId: user.id },
      update: { name: f.name, department: f.department },
      create: { userId: user.id, name: f.name, department: f.department },
    });

    facultyProfiles.push(faculty);
  }
  console.log('Faculty users & profiles created:', facultyProfiles.map(x => x.id));

  // 4) Subjects (4)
  const subjectNames = ['Physics', 'Chemistry', 'Mathematics', 'Computer Science'];
  const subjects = [];
  for (const s of subjectNames) {
    const rec = await prisma.subject.upsert({
      where: { name: s },
      update: {},
      create: { name: s },
    });
    subjects.push(rec);
  }
  console.log('Subjects upserted:', subjects.map(s => s.name));

  // 5) Classes (2) - use findFirst/create because name is not unique in schema
  const classInput = [
    { name: 'BSc CS - A', facultyId: facultyProfiles[0].id },
    { name: 'BSc Maths - A', facultyId: facultyProfiles[1].id },
  ];

  const classRecords = [];
  for (const c of classInput) {
    // find existing class by name AND facultyId
    let cls = await prisma.class.findFirst({
      where: { name: c.name, facultyId: c.facultyId }
    });

    if (!cls) {
      cls = await prisma.class.create({
        data: { name: c.name, facultyId: c.facultyId }
      });
    } else {
      // ensure facultyId is updated if needed
      if (cls.facultyId !== c.facultyId) {
        cls = await prisma.class.update({
          where: { id: cls.id },
          data: { facultyId: c.facultyId }
        });
      }
    }
    classRecords.push(cls);
  }
  console.log('Classes prepared:', classRecords.map(c => ({ id: c.id, name: c.name })));

  // 6) Link Classes <-> Subjects (2 subjects per class)
  const map = [
    { classId: classRecords[0].id, subjectName: 'Computer Science' },
    { classId: classRecords[0].id, subjectName: 'Physics' },
    { classId: classRecords[1].id, subjectName: 'Mathematics' },
    { classId: classRecords[1].id, subjectName: 'Chemistry' },
  ];

  const classSubjectData = map.map(m => ({
    classId: m.classId,
    subjectId: subjects.find(s => s.name === m.subjectName).id
  }));

  // createMany (idempotent)
  await prisma.classSubject.createMany({
    data: classSubjectData,
  });
  console.log('ClassSubject links created.');

  // 7) Create students & parents (5 students per class)
  const studentsPerClass = 5;
  let studentGlobalIndex = 1;
  const createdStudents = [];

  for (let ci = 0; ci < classRecords.length; ci++) {
    const cls = classRecords[ci];

    for (let i = 0; i < studentsPerClass; i++) {
      const studentEmail = `student${studentGlobalIndex}@example.com`;
      const studentName = `Student ${studentGlobalIndex}`;
      const studentPassword = 'student123';
      const hashedStudent = await bcrypt.hash(studentPassword, 10);

      // create user (student)
      const stuUser = await prisma.user.upsert({
        where: { email: studentEmail },
        update: { passwordHash: hashedStudent, roleId: roleStudent.id },
        create: { email: studentEmail, passwordHash: hashedStudent, roleId: roleStudent.id }
      });

      // create student profile
      const enrollmentNo = `${cls.name.replace(/\s+/g, '_')}_ENR_${String(studentGlobalIndex).padStart(3, '0')}`;

      const studentProfile = await prisma.student.upsert({
        where: { userId: stuUser.id },
        update: { name: studentName, classId: cls.id, enrollmentNo },
        create: { userId: stuUser.id, name: studentName, classId: cls.id, enrollmentNo }
      });

      // create parent user/profile
      const parentEmail = `parent${studentGlobalIndex}@example.com`;
      const parentName = `Parent ${studentGlobalIndex}`;
      const parentPassword = 'parent123';
      const hashedParent = await bcrypt.hash(parentPassword, 10);

      const parentUser = await prisma.user.upsert({
        where: { email: parentEmail },
        update: { passwordHash: hashedParent, roleId: roleParent.id },
        create: { email: parentEmail, passwordHash: hashedParent, roleId: roleParent.id }
      });

      const parentProfile = await prisma.parent.upsert({
        where: { userId: parentUser.id },
        update: { name: parentName, phone: `+91123456${String(studentGlobalIndex).padStart(2, '0')}` },
        create: { userId: parentUser.id, name: parentName, phone: `+91123456${String(studentGlobalIndex).padStart(2, '0')}` }
      });

      // connect student and parent
      await prisma.studentParent.createMany({
        data: [{ studentId: studentProfile.id, parentId: parentProfile.id }],
      });

      createdStudents.push({ studentProfile, stuUser, parentProfile });
      studentGlobalIndex++;
    }
  }

  console.log(`Created ${createdStudents.length} students (and parents).`);

  // Summary
  console.log('Seed summary:');
  console.log('- Classes:', classRecords.map(c => ({ id: c.id, name: c.name })));
  console.log('- Subjects:', subjects.map(s => ({ id: s.id, name: s.name })));
  console.log('- Sample student:', createdStudents[0] ? { id: createdStudents[0].studentProfile.id, name: createdStudents[0].studentProfile.name } : 'none');

  console.log('Seeding finished successfully.');
}

main()
  .catch(e => {
    console.error('Seed error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
