const express = require('express');
const authenticateToken = require('../middleware/auth');
const permit = require('../middleware/rbac');
const analyticsController = require('../controllers/analyticsController');

const router = express.Router();

// Student-specific analytics
router.get('/student', authenticateToken, permit('student'), analyticsController.studentAnalytics);

// Faculty-specific analytics
router.get('/faculty', authenticateToken, permit('faculty'), analyticsController.facultyAnalytics);

// Admin-specific analytics
router.get('/admin', authenticateToken, permit('admin'), analyticsController.adminAnalytics);

module.exports = router;
