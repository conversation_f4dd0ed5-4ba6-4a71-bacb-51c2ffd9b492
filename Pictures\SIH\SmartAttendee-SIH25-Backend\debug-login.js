const axios = require('axios');

async function debugLogin() {
  try {
    console.log('🔍 Debugging login flow...\n');
    
    // Step 1: Test server health
    console.log('1. Testing server health...');
    try {
      const healthResponse = await axios.get('http://localhost:5001/', { timeout: 5000 });
      console.log('✅ Server health OK:', healthResponse.data.message);
    } catch (error) {
      console.log('❌ Server health failed:', error.message);
      return;
    }

    // Step 2: Test login endpoint
    console.log('\n2. Testing login endpoint...');
    try {
      const loginResponse = await axios.post('http://localhost:5001/auth/login', {
        email: '<EMAIL>',
        password: 'faculty123'
      }, { timeout: 5000 });
      
      console.log('✅ Login successful!');
      console.log('Response status:', loginResponse.status);
      console.log('Response data:', JSON.stringify(loginResponse.data, null, 2));
      
      const { token, user } = loginResponse.data;
      
      if (token) {
        console.log('✅ Token received');
        
        // Step 3: Test protected endpoint
        console.log('\n3. Testing protected endpoint...');
        try {
          const analyticsResponse = await axios.get('http://localhost:5001/api/analytics/faculty', {
            headers: {
              'Authorization': `Bearer ${token}`
            },
            timeout: 5000
          });
          
          console.log('✅ Protected endpoint works!');
          console.log('Faculty name:', analyticsResponse.data.facultyName);
          console.log('Number of classes:', analyticsResponse.data.classes?.length || 0);
          
        } catch (error) {
          console.log('❌ Protected endpoint failed:', error.response?.data?.message || error.message);
        }
        
      } else {
        console.log('❌ No token in response');
      }
      
    } catch (error) {
      console.log('❌ Login failed:');
      console.log('Status:', error.response?.status);
      console.log('Message:', error.response?.data?.message || error.message);
      console.log('Full error:', error.response?.data);
    }
    
  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
  }
}

debugLogin();
