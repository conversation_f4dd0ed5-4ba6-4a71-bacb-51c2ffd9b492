import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  <PERSON><PERSON><PERSON>,
  Bar,
} from "recharts";

// Dummy data
const lineData = [
  { month: "Jan", attendance: 80 },
  { month: "Feb", attendance: 60 },
  { month: "Mar", attendance: 75 },
  { month: "Apr", attendance: 90 },
  { month: "May", attendance: 85 },
  { month: "Jun", attendance: 70 },
];

const pieData = [
  { name: "Class A", value: 90 },
  { name: "Class B", value: 80 },
  { name: "Class C", value: 75 },
  { name: "Class D", value: 95 },
];

const barData = [
  { faculty: "F1", engagement: 85 },
  { faculty: "F2", engagement: 65 },
  { faculty: "F3", engagement: 50 },
  { faculty: "F4", engagement: 70 },
  { faculty: "F5", engagement: 60 },
  { faculty: "F6", engagement: 75 },
];

const COLORS = ["#6366F1", "#8B5CF6", "#06B6D4", "#F59E0B"];

const AdminAnalyticsOverview = () => {
  return (
    <div className="p-6 space-y-16 bg-gray-50 min-h-screen">
      {/* Analytics Overview */}
      <section className="text-center">
        <h1 className="text-3xl font-extrabold text-gray-800">Analytics Overview</h1>
        <p className="text-gray-500 mt-2">
          Insights into attendance, engagement, and class performance.
        </p>
        <div className="flex justify-center space-x-4 mt-6">
          <Button className="bg-white text-indigo-700 hover:bg-gray-100 active:bg-indigo-700 active:text-white px-6 py-2 rounded-full shadow-md transition">
            Refresh Data
          </Button>
          <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:opacity-90 active:bg-white active:text-indigo-700 px-6 py-2 rounded-full shadow-md transition">
            Download Report
          </Button>
        </div>
        <div className="flex justify-center space-x-2 mt-4">
          <Button className="bg-indigo-100 text-indigo-700 hover:bg-indigo-200 px-3 py-1 rounded-md text-sm">
            Attendance
          </Button>
          <Button className="bg-purple-100 text-purple-700 hover:bg-purple-200 px-3 py-1 rounded-md text-sm">
            Engagement
          </Button>
          <Button className="bg-teal-100 text-teal-700 hover:bg-teal-200 px-3 py-1 rounded-md text-sm">
            Trends
          </Button>
        </div>
      </section>

      {/* Key Statistics */}
      <section className="max-w-6xl mx-auto">
        <h2 className="text-2xl font-bold text-center text-gray-800">Key Statistics</h2>
        <p className="text-center text-gray-500 mt-1">Important metrics at a glance.</p>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6">
          <Card className="shadow-lg rounded-2xl">
            <CardContent className="p-6 text-center">
              <p className="text-3xl font-bold text-indigo-600">120</p>
              <p className="text-gray-600">Total Classes</p>
              <span className="text-green-600 text-sm">+5%</span>
            </CardContent>
          </Card>
          <Card className="shadow-lg rounded-2xl">
            <CardContent className="p-6 text-center">
              <p className="text-3xl font-bold text-purple-600">85%</p>
              <p className="text-gray-600">Average Attendance</p>
              <span className="text-green-600 text-sm">+2%</span>
            </CardContent>
          </Card>
          <Card className="shadow-lg rounded-2xl">
            <CardContent className="p-6 text-center">
              <p className="text-3xl font-bold text-teal-600">75%</p>
              <p className="text-gray-600">Faculty Engagement</p>
              <span className="text-red-600 text-sm">-1%</span>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Attendance Trends */}
      <section className="max-w-6xl mx-auto">
        <h2 className="text-2xl font-bold text-center text-gray-800">Attendance Trends</h2>
        <p className="text-center text-gray-500 mt-1">Visual representation of attendance rates.</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
          <Card className="p-4 shadow-lg rounded-2xl">
            <p className="mb-2 font-semibold text-gray-700">Monthly Attendance Trend</p>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={lineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="attendance" stroke="#6366F1" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </Card>

          <Card className="p-4 shadow-lg rounded-2xl">
            <p className="mb-2 font-semibold text-gray-700">Attendance Distribution</p>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie data={pieData} dataKey="value" outerRadius={80} label>
                  {pieData.map((_, index) => (
                    <Cell key={index} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </div>
      </section>

      {/* Class Performance */}
      <section className="max-w-6xl mx-auto text-center">
        <h2 className="text-2xl font-bold text-gray-800">Class Performance</h2>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6">
          <Card className="shadow-md rounded-2xl">
            <CardContent className="p-6">
              <p className="text-xl font-bold text-indigo-600">90%</p>
              <p className="text-gray-600">Class A</p>
              <span className="text-green-600 text-sm">+3%</span>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-2xl">
            <CardContent className="p-6">
              <p className="text-xl font-bold text-purple-600">80%</p>
              <p className="text-gray-600">Class B</p>
              <span className="text-green-600 text-sm">+5%</span>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-2xl">
            <CardContent className="p-6">
              <p className="text-xl font-bold text-teal-600">75%</p>
              <p className="text-gray-600">Class C</p>
              <span className="text-red-600 text-sm">-2%</span>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Classes by Attendance */}
      <section className="max-w-6xl mx-auto text-center">
        <h2 className="text-2xl font-bold text-gray-800">Classes by Attendance</h2>
        <p className="text-gray-500">Detailed class attendance percentages.</p>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 mt-6">
          {pieData.map((c, i) => (
            <div key={i} className="flex flex-col items-center">
              <div className="w-16 h-16 rounded-full flex items-center justify-center text-white font-bold shadow-md"
                   style={{ backgroundColor: COLORS[i % COLORS.length] }}>
                {c.name.split(" ")[1]}
              </div>
              <p className="mt-2 font-medium">{c.name}</p>
              <p className="text-gray-600">{c.value}%</p>
            </div>
          ))}
        </div>
      </section>

      {/* Faculty Engagement */}
      <section className="max-w-6xl mx-auto text-center">
        <h2 className="text-2xl font-bold text-gray-800">Faculty Engagement Analysis</h2>
        <p className="text-gray-500">Engagement metrics by faculty.</p>
        <Card className="mt-6 p-4 shadow-lg rounded-2xl">
          <p className="mb-2 font-semibold text-gray-700">Faculty-Wise Engagement</p>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={barData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="faculty" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="engagement" fill="#8B5CF6" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </section>

      {/* Footer */}
      <footer className="text-center text-gray-500 text-sm mt-16 border-t pt-6">
        © 2023 Admin Dashboard ·{" "}
        <a href="#" className="hover:underline">Privacy Policy</a> ·{" "}
        <a href="#" className="hover:underline">Terms of Service</a>
      </footer>
    </div>
  );
};

export default AdminAnalyticsOverview;
