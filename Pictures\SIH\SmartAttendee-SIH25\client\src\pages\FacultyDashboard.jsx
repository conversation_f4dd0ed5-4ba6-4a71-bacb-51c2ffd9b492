import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Calendar,
  Clock,
  QrCode,
  RefreshCw,
  Users,
  BookOpen,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { facultyAPI, attendanceAPI } from '../services/api';
import axios from 'axios';

const FacultyDashboard = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);
  const [qrGenerating, setQrGenerating] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedClass, setSelectedClass] = useState(null);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [activeQRSession, setActiveQRSession] = useState(null);
  const [qrTimeRemaining, setQrTimeRemaining] = useState(0);

  // Error handling helper per documentation
  const getErrorMessage = (error) => {
    const status = error.response?.status;
    const message = error.response?.data?.message;

    switch (status) {
      case 400:
        return message || "Please check input";
      case 401:
        return "Session expired. Please sign in again.";
      case 403:
        return "You don't have access to this resource.";
      case 500:
        return "Server error. Try again later.";
      default:
        return message || "Failed to load dashboard data. Please check your connection and try again.";
    }
  };

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching faculty analytics...');
      console.log('Auth token exists:', !!localStorage.getItem('authToken'));
      console.log('Axios auth header:', axios.defaults.headers.common['Authorization']);

      const response = await facultyAPI.getAnalytics();
      console.log('Analytics response received:', response.data);
      setDashboardData(response.data);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      console.error('Error details:', {
        status: err.response?.status,
        message: err.response?.data?.message,
        data: err.response?.data
      });

      const errorMessage = getErrorMessage(err);
      setError(errorMessage);

      // Handle 401 - redirect to login
      if (err.response?.status === 401) {
        localStorage.removeItem('authToken');
        delete axios.defaults.headers.common['Authorization'];
        console.log('Token expired, cleared auth data');
      }

      setDashboardData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  // Check authentication and fetch data
  useEffect(() => {
    const initializeFaculty = async () => {
      console.log('🏫 Faculty Dashboard initializing...');

      // Check if user is authenticated
      const token = localStorage.getItem('authToken');
      const userRole = localStorage.getItem('userRole');
      const userEmail = localStorage.getItem('userEmail');

      console.log('🔍 Authentication check:');
      console.log('  - Token:', token ? `Found (${token.substring(0, 20)}...)` : 'Not found');
      console.log('  - Role:', userRole || 'Not set');
      console.log('  - Email:', userEmail || 'Not set');

      if (!token) {
        console.log('❌ No token found, redirecting to login');
        setError('Please login to access the faculty dashboard');
        navigate('/login');
        return;
      }

      console.log('✅ Token found, setting up authentication and fetching data');
      // Set token in axios headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // Fetch dashboard data
      console.log('📊 Fetching dashboard data...');
      fetchDashboardData();
    };

    initializeFaculty();
  }, [fetchDashboardData, navigate]);

  // Real-time clock
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // QR session timer countdown per documentation (5 minutes expiry)
  useEffect(() => {
    if (qrTimeRemaining > 0) {
      const timer = setInterval(() => {
        setQrTimeRemaining(prev => {
          if (prev <= 1) {
            setActiveQRSession(null);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [qrTimeRemaining]);

  // Set default class and subject when data loads
  useEffect(() => {
    if (dashboardData?.classes?.length > 0 && !selectedClass) {
      const firstClass = dashboardData.classes[0];
      setSelectedClass(firstClass);
      if (firstClass.subjects?.length > 0) {
        setSelectedSubject(firstClass.subjects[0]);
      }
    }
  }, [dashboardData, selectedClass]);



  // Generate upcoming classes from real data - Updated per backend API response
  const getUpcomingClasses = () => {
    if (!dashboardData?.classes) return [];

    const upcomingClasses = [];
    dashboardData.classes.forEach(cls => {
      // Backend analytics returns subjects array with subjectId, subjectName
      cls.subjects?.forEach(subject => {
        upcomingClasses.push({
          id: `${cls.classId}-${subject.subjectId}`,
          subject: subject.subjectName,
          className: cls.className,
          students: cls.totalStudents,
          time: 'TBD', // Time not available in analytics API
          room: 'TBD', // Room not available in analytics API
          status: 'upcoming'
        });
      });
    });

    return upcomingClasses.slice(0, 3); // Show only first 3
  };

  const upcomingClasses = getUpcomingClasses();

  // Process real backend data - Faculty Analytics API response structure
  const firstClass = dashboardData?.classes?.[0];
  const analyticsData = firstClass?.overall || {
    attendancePct: 0,
    totalSessions: 0,
    presentCount: 0,
    possibleSeats: 0
  };

  // Calculate live attendance data from real backend data
  const totalStudents = firstClass?.totalStudents || 0;
  const attendancePct = analyticsData.attendancePct || 0;

  const liveAttendanceData = {
    totalStudents: totalStudents,
    currentlyPresent: Math.floor((attendancePct / 100) * totalStudents),
    totalAbsentees: totalStudents - Math.floor((attendancePct / 100) * totalStudents),
    recentChange: 'N/A', // Can be calculated from last7days data if needed
    absenteeChange: 'N/A' // Can be calculated from last7days data if needed
  };

  const handleGenerateQR = async () => {
    try {
      setQrGenerating(true);
      setError(null);

      // Frontend validation per documentation
      if (!selectedClass || !selectedSubject) {
        alert('Please select both class and subject before generating QR code');
        return;
      }

      // Get current location for QR generation
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser');
      }

      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
        );
      });

      // Validate coordinates are provided in decimal degrees per documentation
      const { latitude, longitude } = position.coords;
      if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
        throw new Error('Invalid location coordinates');
      }

      // QR data structure per backend API: classId, subjectId, geoLat, geoLng
      const qrData = {
        classId: selectedClass.classId,
        subjectId: selectedSubject.subjectId,
        geoLat: latitude,
        geoLng: longitude
      };

      // Validate required fields before sending per backend requirements
      if (!qrData.classId || !qrData.subjectId || qrData.geoLat === undefined || qrData.geoLng === undefined) {
        throw new Error('Missing required fields for QR generation');
      }

      const response = await attendanceAPI.generateQR(qrData);

      // Success response per documentation: { qrSessionId, generatedAt, message }
      const { qrSessionId, generatedAt, message } = response.data;

      // Set active QR session and start timer (5 minutes per documentation)
      setActiveQRSession({
        id: qrSessionId,
        generatedAt: new Date(generatedAt),
        classId: qrData.classId,
        subjectId: qrData.subjectId
      });
      setQrTimeRemaining(300); // 5 minutes in seconds

      alert(`${message || 'QR Code generated successfully!'}\nSession ID: ${qrSessionId}\nValid for 5 minutes`);

    } catch (err) {
      console.error('Error generating QR:', err);
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);

      // Handle specific QR generation errors per documentation
      if (err.response?.status === 403) {
        alert('You are not assigned to this class');
      } else if (err.response?.status === 400) {
        alert(err.response?.data?.message || 'Invalid class or subject selection');
      } else {
        alert(errorMessage);
      }
    } finally {
      setQrGenerating(false);
    }
  };

  const handleViewDetails = () => {
    // Navigate to detailed analytics page
    navigate('/faculty/analytics');
  };

  const handleRefreshAttendance = async () => {
    try {
      setRefreshing(true);
      await fetchDashboardData();
      alert('Attendance data refreshed successfully!');
    } catch (err) {
      console.error('Error refreshing data:', err);
      alert('Failed to refresh data. Please try again.');
    } finally {
      setRefreshing(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // No data state
  if (!dashboardData || !dashboardData.classes || dashboardData.classes.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Classes Assigned</h2>
          <p className="text-gray-600 mb-4">
            {dashboardData ?
              "You don't have any classes assigned yet. Please contact the administrator." :
              "Unable to load faculty dashboard data. Please try again later."
            }
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-yellow-600" />
          <p className="text-yellow-800">{error}</p>
        </div>
      )}

      {/* Faculty Info Header */}
      {dashboardData && (
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Welcome, {dashboardData.facultyName}</h1>
              <p className="text-blue-100 mt-1">Faculty Dashboard - {currentTime.toLocaleDateString()}</p>
              <p className="text-blue-200 text-sm">
                <Clock className="w-4 h-4 inline mr-1" />
                {currentTime.toLocaleTimeString()}
              </p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold">{dashboardData.classes?.length || 0}</div>
              <div className="text-blue-100">Classes Managed</div>
            </div>
          </div>
        </div>
      )}
          {/* QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <QrCode className="w-6 h-6" />
                <span>Today's QR Code</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600">Generate QR Code for attendance</p>

                {/* Class and Subject Selection */}
                {dashboardData?.classes && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Class</label>
                      <select
                        value={selectedClass?.classId || ''}
                        onChange={(e) => {
                          const classId = parseInt(e.target.value);
                          const cls = dashboardData.classes.find(c => c.classId === classId);
                          setSelectedClass(cls);
                          if (cls?.subjects?.length > 0) {
                            setSelectedSubject(cls.subjects[0]);
                          }
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {dashboardData.classes.map((cls) => (
                          <option key={cls.classId} value={cls.classId}>
                            {cls.className}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                      <select
                        value={selectedSubject?.subjectId || ''}
                        onChange={(e) => {
                          const subjectId = parseInt(e.target.value);
                          const subject = selectedClass?.subjects?.find(s => s.subjectId === subjectId);
                          setSelectedSubject(subject);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={!selectedClass?.subjects?.length}
                      >
                        {selectedClass?.subjects?.map((subject) => (
                          <option key={subject.subjectId} value={subject.subjectId}>
                            {subject.subjectName}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleGenerateQR}
                  disabled={qrGenerating || !selectedClass || !selectedSubject}
                  className="w-full bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {qrGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      <span>Generating QR Code...</span>
                    </>
                  ) : (
                    <>
                      <QrCode className="w-4 h-4" />
                      <span>Generate QR Code for {selectedSubject?.subjectName || 'Selected Subject'}</span>
                    </>
                  )}
                </Button>

                {/* QR Session Status - Show countdown timer per documentation */}
                {activeQRSession && qrTimeRemaining > 0 && (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-green-800">Active QR Session</h4>
                        <p className="text-sm text-green-600">
                          Session ID: {activeQRSession.id}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-800">
                          {Math.floor(qrTimeRemaining / 60)}:{(qrTimeRemaining % 60).toString().padStart(2, '0')}
                        </div>
                        <p className="text-xs text-green-600">Time remaining</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Analytics Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-center">
                <span className="text-2xl font-bold">Quick Analytics</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6 text-center">Class attendance summary</p>

              <div className="flex justify-center mb-6">
                <Button
                  onClick={handleViewDetails}
                  className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-2 rounded-lg"
                >
                  View Details
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-2">Overall Attendance Rate</p>
                  <p className="text-4xl font-bold text-gray-800">{analyticsData.attendancePct}%</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-2">Total Sessions</p>
                  <p className="text-4xl font-bold text-gray-800">{analyticsData.totalSessions}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-2">Present Students</p>
                  <p className="text-4xl font-bold text-gray-800">{analyticsData.presentCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Live Attendance Status Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-center">
                <span className="text-2xl font-bold">Live Attendance Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6 text-center">Current attendance for today's classes</p>

              <div className="flex justify-center mb-6">
                <Button
                  onClick={handleRefreshAttendance}
                  disabled={refreshing}
                  className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-2 rounded-lg flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                  <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-2">Total Students</p>
                  <p className="text-4xl font-bold text-gray-800">{liveAttendanceData.totalStudents}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-2">Currently Present</p>
                  <p className="text-4xl font-bold text-gray-800">{liveAttendanceData.currentlyPresent}</p>
                  <p className="text-green-600 text-sm font-medium">{liveAttendanceData.recentChange}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-2">Total Absentees</p>
                  <p className="text-4xl font-bold text-gray-800">{liveAttendanceData.totalAbsentees}</p>
                  <p className="text-red-600 text-sm font-medium">{liveAttendanceData.absenteeChange}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Classes Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="w-6 h-6" />
                <span>Upcoming Classes</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6">List of your upcoming classes</p>

              <div className="space-y-4">
                {upcomingClasses.map((classItem) => (
                  <div
                    key={classItem.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <Clock className="w-6 h-6 text-gray-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800">{classItem.subject}</h3>
                        <p className="text-gray-600">{classItem.room}</p>
                      </div>
                    </div>

                    <div className="text-right">
                      <p className="font-semibold text-gray-800">Starts at {classItem.time}</p>
                      <p className="text-sm text-gray-600">{classItem.students} students</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Subjects Overview */}
          {dashboardData?.classes?.[0]?.subjects && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                  <span>Subjects Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {dashboardData.classes[0].subjects.map((subject) => (
                    <div key={subject.subjectId} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-800 mb-2">{subject.subjectName}</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Sessions:</span>
                          <span className="font-medium">{subject.totalSessions || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Present:</span>
                          <span className="font-medium">{subject.presentCount || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Attendance:</span>
                          <span className={`font-medium ${(subject.attendancePct || 0) >= 80 ? 'text-green-600' : 'text-orange-600'}`}>
                            {Math.round(subject.attendancePct || 0)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Suggestions */}
          {dashboardData?.suggestions && dashboardData.suggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <span>Suggestions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dashboardData.suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-green-800 text-sm">{suggestion}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
    </div>
  );
};

export default FacultyDashboard;