const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
require('dotenv').config();

const register = async (req, res) => {
  try {
    const { email, password, name, role } = req.body;

    // Checking if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: 'Email already registered' });
    }

    // Find role
    const roleRecord = await prisma.role.findUnique({ where: { name: role } });
    if (!roleRecord) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    await prisma.user.create({
      data: {
        email,
        passwordHash: hashedPassword,
        roleId: roleRecord.id,
      },
    });

    res.status(201).json({ message: 'User registered successfully' });
  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        role: true,
        student: true,
        faculty: true,
        parent: true
      }
    });
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Compare password
    const isMatch = await bcrypt.compare(password, user.passwordHash);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Create token
    const token = jwt.sign(
      { userId: user.id, role: user.role.name },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    // Get user details for response
    let userDetails = { id: user.id, email: user.email, role: user.role.name };

    // Get name from related model
    if (user.student) {
      userDetails.name = user.student.name;
    } else if (user.faculty) {
      userDetails.name = user.faculty.name;
    } else if (user.parent) {
      userDetails.name = user.parent.name;
    }

    res.json({
      token,
      user: userDetails
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

const profile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        role: true,
        student: true,
        faculty: true,
        parent: true
      }
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get name from related model
    let name = null;
    if (user.student) name = user.student.name;
    else if (user.faculty) name = user.faculty.name;
    else if (user.parent) name = user.parent.name;

    res.json({
      id: user.id,
      email: user.email,
      name,
      role: user.role.name
    });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  register,
  login,
  profile
};
