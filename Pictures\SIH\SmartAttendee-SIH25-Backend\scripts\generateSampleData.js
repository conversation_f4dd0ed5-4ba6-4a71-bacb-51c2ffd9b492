// scripts/generateSampleData.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function generateSampleData() {
  console.log('Generating sample QR sessions and attendance records...');

  try {
    // Get faculty and class data
    const faculty = await prisma.faculty.findFirst({ where: { id: 1 } });
    const classes = await prisma.class.findMany({ include: { students: true, subjects: true } });
    const subjects = await prisma.subject.findMany();

    if (!faculty || classes.length === 0) {
      console.log('No faculty or classes found. Please run seed script first.');
      return;
    }

    // Generate QR sessions for the past 3 days (simplified)
    const today = new Date();
    const sessions = [];

    for (let dayOffset = 2; dayOffset >= 0; dayOffset--) {
      const sessionDate = new Date(today);
      sessionDate.setDate(today.getDate() - dayOffset);
      sessionDate.setHours(10, 0, 0, 0); // 10 AM

      // Just create one session per day for the first class
      const cls = classes[0];
      const classSubject = cls.subjects[0];

      const session = await prisma.qRSession.create({
        data: {
          facultyId: faculty.id,
          classId: cls.id,
          subjectId: classSubject.subjectId,
          geoLat: 28.6139, // Delhi coordinates
          geoLng: 77.2090,
          isActive: false, // Past sessions are inactive
          generatedAt: sessionDate
        }
      });
      sessions.push(session);
      console.log(`Created session ${session.id} for ${sessionDate.toDateString()}`);
    }

    console.log(`Created ${sessions.length} QR sessions`);

    // Generate attendance records for these sessions
    let totalAttendanceRecords = 0;

    for (const session of sessions) {
      const classData = classes.find(c => c.id === session.classId);
      if (!classData) continue;

      // Simulate 70-90% attendance
      const attendanceRate = 0.7 + Math.random() * 0.2;
      const studentsToMark = Math.floor(classData.students.length * attendanceRate);

      // Randomly select students to mark as present
      const shuffledStudents = [...classData.students].sort(() => Math.random() - 0.5);
      const presentStudents = shuffledStudents.slice(0, studentsToMark);

      for (const student of presentStudents) {
        await prisma.attendanceRecord.create({
          data: {
            studentId: student.id,
            classId: session.classId,
            subjectId: session.subjectId,
            status: 'present',
            qrSessionId: session.id,
            datetime: new Date(session.generatedAt.getTime() + Math.random() * 30 * 60 * 1000) // Within 30 minutes
          }
        });
        totalAttendanceRecords++;
      }
    }

    console.log(`Created ${totalAttendanceRecords} attendance records`);

    // Generate a few active QR sessions for today
    const activeSession = await prisma.qRSession.create({
      data: {
        facultyId: faculty.id,
        classId: classes[0].id,
        subjectId: classes[0].subjects[0].subjectId,
        geoLat: 28.6139,
        geoLng: 77.2090,
        isActive: true,
        generatedAt: new Date()
      }
    });

    console.log(`Created active QR session: ${activeSession.id}`);
    console.log('Sample data generation completed successfully!');

  } catch (error) {
    console.error('Error generating sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

generateSampleData();
