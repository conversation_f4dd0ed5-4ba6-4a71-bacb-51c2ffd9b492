import React, { useState } from "react";
import { useNavigate } from "react-router";

export default function UserLogin() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState("");
  const [submitted, setSubmitted] = useState(false);
 const navigate = useNavigate();
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!role) {
      alert("Please select a role before submitting!");
      return;
    }
 try {
      const res = await fetch(
        "https://smartattendee-sih25-backend.onrender.com/auth/login",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email, password }),
        }
      );

      if (!res.ok) throw new Error("Login failed");
      //data is added
const data = await res.json();
     const expiryTime = Date.now() + 3600000;

    localStorage.setItem("auth", JSON.stringify({
      token: data.token,
      role,
      email,
      expiry: expiryTime
    }));
      setSubmitted(true);
      navigate("/");
    } catch (err) {
      console.error(err);
      alert("Invalid credentials");
    }
  };

  if (submitted) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-3xl font-bold text-gray-800">Welcome {role}!</h2>
        <p className="mt-2 text-lg text-gray-600">
          You have successfully logged in with {email}
        </p>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col items-center justify-center w-full space-y-6 px-6"
    >
      <h2 className="text-3xl font-bold text-gray-800 mb-4">User Login</h2>

     
      <div className="flex flex-col items-start w-full max-w-sm">
        <label className="mb-1 text-sm font-medium text-gray-700">Email:</label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
      </div>

     
      <div className="flex flex-col items-start w-full max-w-sm">
        <label className="mb-1 text-sm font-medium text-gray-700">Password:</label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
      </div>

      
      <div className="flex flex-col items-start w-full max-w-sm mt-2">
        <label className="mb-1 text-sm font-medium text-gray-700">
          Select your role:
        </label>
        <div className="flex gap-3 flex-wrap">
          {["Student", "Teacher", "Parent"].map((r) => (
            <button
              key={r}
              type="button"
              className={`rounded-lg px-6 py-2 font-semibold border transition-all duration-300
              ${
                role === r
                  ? "bg-indigo-600 text-white border-indigo-600 shadow-md scale-105"
                  : "bg-gray-200 text-gray-800 border-gray-300 hover:bg-gray-300"
              }`}
              onClick={() => setRole(r)}
            >
              {r}
            </button>
          ))}
        </div>
      </div>

      
      <button
        type="submit"
        className="rounded-lg bg-indigo-600 text-white px-8 py-2 font-semibold 
        shadow-md transition-all duration-300 hover:bg-indigo-700 active:bg-white active:text-indigo-700 mt-4"
      >
        Submit
      </button>
    </form>
  );
}
