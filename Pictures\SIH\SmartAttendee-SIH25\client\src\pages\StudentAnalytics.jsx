import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
import { Mail, MessageSquare, FileDown } from "lucide-react";

const COLORS = ["#6366F1", "#E5E7EB"];

const StudentAnalyticsPage = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

 useEffect(() => {
    const fetchAnalytics = async () => {
       const authData = JSON.parse(localStorage.getItem("auth"));
const token = authData?.token;// stored at login
      try {
        const res = await fetch(
          "https://smartattendee-sih25-backend.onrender.com/api/analytics/student",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
const dummyData = await res.json(); // keep the variable name as dummyData
        // Transform weekChart for recharts if it exists
        const chartData = dummyData?.weekChart?.labels.map((label, i) => ({
          week: label,
          attendance: dummyData?.weekChart?.values[i] ?? 0,
        }));

        setData({ ...dummyData, chartData });
      } catch (err) {
        console.error("Error fetching analytics", err);
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);
  


  if (loading) return <p className="text-center">Loading analytics...</p>;
  if (!data) return <p className="text-center text-red-500">No analytics data</p>;

  return (
    <div className="flex gap-6 p-6">
      {/* Left Sidebar */}
      <div className="w-1/4 bg-white shadow rounded-2xl p-6 flex flex-col items-center">
        <img
          src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
            data?.studentName
          )}&background=6366F1&color=fff&size=128`}
          alt="Student"
          className="w-24 h-24 rounded-full mb-4"
        />
        <h2 className="text-xl font-bold">{data.studentName}</h2>
        <p className="text-gray-600">{data.className}</p>
        <p className="text-sm text-gray-500 mt-1">
          Last Updated: {new Date(data.lastUpdated).toLocaleDateString()}
        </p>

        {/* Badges */}
        <div className="flex gap-2 mt-3 flex-wrap justify-center">
          {data?.badges?.map((badge, idx) => (
            <span
              key={idx}
              className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs"
            >
              {badge}
            </span>
          ))}
        </div>

        <div className="w-full mt-6 space-y-2">
          <button className="w-full flex items-center justify-center gap-2 bg-indigo-50 hover:bg-indigo-100 py-2 rounded-lg">
            <Mail size={16} /> Message Student
          </button>
          <button className="w-full flex items-center justify-center gap-2 bg-indigo-50 hover:bg-indigo-100 py-2 rounded-lg">
            <MessageSquare size={16} /> Request Meeting
          </button>
          <button className="w-full flex items-center justify-center gap-2 bg-indigo-50 hover:bg-indigo-100 py-2 rounded-lg">
            <FileDown size={16} /> Export PDF
          </button>
        </div>
      </div>

      {/* Right Analytics */}
      <div className="flex-1 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Overall Attendance */}
          <div className="bg-white border rounded-2xl shadow-sm p-6 text-center relative">
            <h3 className="text-sm font-medium text-gray-500 mb-3">
              Overall Attendance
            </h3>
            <ResponsiveContainer width={120} height={120}>
              <PieChart>
                <Pie
                  data={[
                    { name: "Present", value: data.overall.attendancePct },
                    { name: "Absent", value: 100 - data.overall.attendancePct },
                  ]}
                  innerRadius={40}
                  outerRadius={55}
                  dataKey="value"
                >
                  <Cell fill={COLORS[0]} />
                  <Cell fill={COLORS[1]} />
                </Pie>
              </PieChart>
            </ResponsiveContainer>
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <p className="text-2xl font-bold text-indigo-600">
                {data.overall.attendancePct}%
              </p>
              <p className="text-xs text-gray-400">Performance</p>
            </div>
          </div>

          {/* Total Sessions */}
          <div className="bg-white border rounded-2xl shadow-sm p-6 text-center">
            <h3 className="text-sm font-medium text-gray-500">Total Sessions</h3>
            <p className="text-3xl font-bold text-green-600 mt-2">
              {data.overall.totalSessions}
            </p>
            <p className="text-xs text-gray-400 mt-1">Classes conducted</p>
          </div>

          {/* Present */}
          <div className="bg-white border rounded-2xl shadow-sm p-6 text-center">
            <h3 className="text-sm font-medium text-gray-500">Present</h3>
            <p className="text-3xl font-bold text-blue-600 mt-2">
              {data.overall.totalPresent}
            </p>
            <p className="text-xs text-gray-400 mt-1">Days attended</p>
          </div>
        {/* Absent */}
<div className="bg-white border rounded-2xl shadow-sm p-6 text-center">
  <h3 className="text-sm font-medium text-gray-500">Absent</h3>
  <p className="text-3xl font-bold text-red-600 mt-2">
    {data.overall.totalAbsent}
  </p>
  <p className="text-xs text-gray-400 mt-1">Days missed</p>
</div>
  
        </div>

        {/* Prediction Risk Banner */}
        {data.prediction?.risk && (
          <div className="bg-red-100 text-red-700 p-3 rounded-lg text-center">
            ⚠️ At Risk: Predicted {data.prediction.nextMonthPct}% next month
            (threshold {data.prediction.threshold}%)
          </div>
        )}

        {/* Weekly Chart */}
        <div className="bg-white shadow rounded-2xl p-6">
          <h3 className="text-lg font-bold mb-4">Weekly Attendance</h3>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="week" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="attendance"
                stroke="#6366F1"
                strokeWidth={3}
                dot={{ r: 5 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="bg-white shadow rounded-2xl p-6">
  <h3 className="text-lg font-bold mb-4">Daily Breakdown</h3>
  <table className="w-full text-sm border">
    <thead className="bg-gray-50">
      <tr>
        <th className="border px-3 py-2 text-left">Date</th>
        <th className="border px-3 py-2 text-center">Attendance %</th>
        <th className="border px-3 py-2 text-center">Sessions</th>
        <th className="border px-3 py-2 text-center">Present</th>
      </tr>
    </thead>
    <tbody>
      {data.weekChart.raw.map((day, idx) => (
        <tr key={idx} className="hover:bg-gray-50">
          <td className="border px-3 py-2">{day.date}</td>
          <td className="border px-3 py-2 text-center">
            {day.attendancePct !== null ? `${day.attendancePct}%` : "N/A"}
          </td>
          <td className="border px-3 py-2 text-center">{day.totalSessions}</td>
          <td className="border px-3 py-2 text-center">{day.present}</td>
        </tr>
      ))}
    </tbody>
  </table>
</div>

        {/* Subject Performance + Suggestions */}
        <div className="grid grid-cols-2 gap-6">
          {/* Subject Performance */}
          <div className="bg-white shadow rounded-2xl p-6">
            <h3 className="text-lg font-bold mb-4">Subject Performance</h3>
            <div className="space-y-3">
              {data.subjects.map((sub) => (
                <div key={sub.subjectId} className="border-b pb-2">
                  <p className="font-semibold">{sub.subjectName}</p>
                  <p className="text-sm text-gray-600">
                    Present: {sub.presentCount}, Absent: {sub.absentCount},
                    Attendance: {sub.attendancePct}%
                  </p>
                  {sub.attendancePct < 70 && (
                    <button className="mt-2 bg-red-100 text-red-800 text-xs px-3 py-1 rounded-full">
                      Mark At-Risk
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Suggestions */}
          <div className="bg-white shadow rounded-2xl p-6">
            <h3 className="text-lg font-bold mb-4">Actionable Suggestions</h3>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              {data.suggestions.map((s, idx) => (
                <li key={idx}>{s}</li>
              ))}
            </ul>
          </div>
        </div>

      </div>
    </div>
  );
};

export default StudentAnalyticsPage;
