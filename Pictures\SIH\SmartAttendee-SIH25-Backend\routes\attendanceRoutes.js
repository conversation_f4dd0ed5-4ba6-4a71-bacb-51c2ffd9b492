// routes/attendanceRoutes.js
const express = require('express');
const router = express.Router();
const attendanceController = require('../controllers/attendanceController');
const authenticateToken = require('../middleware/auth');
const permit = require('../middleware/rbac');

// Generate QR – only faculty
router.post('/generate-qr', authenticateToken, permit('faculty'), attendanceController.generateQR);

// Mark attendance – only student
router.post('/mark-attendance', authenticateToken, permit('student'), attendanceController.markAttendance);

module.exports = router;
