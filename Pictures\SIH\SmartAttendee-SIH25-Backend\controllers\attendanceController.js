const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const generateQR = async (req, res) => {
  try {
    const facultyId = req.user.id;
    const { classId, subjectId, geoLat, geoLng } = req.body;

    // Validate required fields
    if (!classId || !subjectId || geoLat === undefined || geoLng === undefined) {
      return res.status(400).json({ message: "classId, subjectId, geoLat, geoLng are required" });
    }

    // Check if this faculty actually teaches this class
    const facultyClass = await prisma.class.findFirst({
      where: {
        id: classId,
        facultyId: facultyId
      },
      include: {
        subjects: true
      }
    });

    if (!facultyClass) {
      return res.status(403).json({ message: "You are not assigned to this class" });
    }

    // Check if subject is assigned to the class
    const subjectAssigned = facultyClass.subjects.some(cs => cs.subjectId === subjectId);
    if (!subjectAssigned) {
      return res.status(400).json({ message: "Subject is not assigned to this class" });
    }

    // Deactivate any existing active QR sessions for this class by this faculty and subject
    await prisma.qRSession.updateMany({
      where: {
        classId: classId,
        facultyId: facultyId,
        subjectId: subjectId,
        isActive: true
      },
      data: {
        isActive: false
      }
    });

    // Create new QR session
    const newSession = await prisma.qRSession.create({
      data: {
        facultyId: facultyId,
        classId: classId,
        subjectId: subjectId,
        geoLat: geoLat,
        geoLng: geoLng,
        isActive: true
      }
    });

    res.json({
      message: "QR session generated successfully",
      qrSessionId: newSession.id,
      generatedAt: newSession.generatedAt
    });
  } catch (error) {
    console.error("Error generating QR:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};


const markAttendance = async (req, res) => {
  try {
    const studentId = req.user.id;
    const { qrSessionId, geoLat, geoLng } = req.body;

    if (!qrSessionId || geoLat === undefined || geoLng === undefined) {
      return res.status(400).json({ message: "qrSessionId, geoLat, geoLng are required" });
    }

    // Find the QR session
    const session = await prisma.qRSession.findUnique({
      where: { id: qrSessionId },
      include: {
        class: {
          include: { subjects: true }
        },
        subject: true
      }
    });

    if (!session || !session.isActive) {
      return res.status(400).json({ message: "Invalid or expired QR session" });
    }

    // Check session expiry (e.g., 5 minutes)
    const sessionDurationMinutes = 5;
    const sessionExpiry = new Date(session.generatedAt);
    sessionExpiry.setMinutes(sessionExpiry.getMinutes() + sessionDurationMinutes);
    if (new Date() > sessionExpiry) {
      return res.status(400).json({ message: "QR session has expired" });
    }

    // Check if QR session has a valid subject
    if (!session.subjectId) {
      return res.status(400).json({ message: "QR session does not have a valid subject" });
    }

    // Check if student is part of the class
    const student = await prisma.student.findUnique({
      where: { userId: studentId }
    });

    if (!student || student.classId !== session.classId) {
      return res.status(403).json({ message: "You are not enrolled in this class" });
    }

    // Check if subject is assigned to the class
    const subjectAssigned = session.class.subjects.some(cs => cs.subjectId === session.subjectId);
    if (!subjectAssigned) {
      return res.status(400).json({ message: "Subject is not assigned to this class" });
    }

    // Check if attendance already exists
    const existingAttendance = await prisma.attendanceRecord.findUnique({
      where: {
        studentId_qrSessionId: {
          studentId: student.id,
          qrSessionId: session.id
        }
      }
    });

    if (existingAttendance) {
      return res.status(400).json({ message: "Attendance already marked" });
    }

    // Validate geolocation distance
    const distance = getDistanceFromLatLonInMeters(geoLat, geoLng, session.geoLat, session.geoLng);
    const allowedDistance = 50; // meters

    if (distance > allowedDistance) {
      return res.status(400).json({ message: "You are not within the allowed area" });
    }

    // Mark attendance
    const attendance = await prisma.attendanceRecord.create({
      data: {
        studentId: student.id,
        classId: session.classId,
        subjectId: session.subjectId,
        status: "present",
        qrSessionId: session.id
      }
    });

    res.json({ message: "Attendance marked successfully", attendance });
  } catch (error) {
    console.error("Error marking attendance:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// function - calculate distance between two coordinates
function getDistanceFromLatLonInMeters(lat1, lon1, lat2, lon2) {
  const R = 6371e3; // Earth radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}

module.exports = {
  generateQR,
  markAttendance
};
