import React from "react";
import {
  Home,
  BarChart2,
  Users,
  Settings,
  LogOut,
  BookOpen,
  Calendar,
  TrendingUp
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
const getNavItems = (currentPath) => {
  if (currentPath.startsWith('/faculty')) {
    return [
      { label: "Dashboard", icon: Home, path: "/faculty" },
      { label: "My Classes", icon: BookOpen, path: "/faculty/classes" },
      { label: "Attendance", icon: Calendar, path: "/faculty/attendance" },
      { label: "Analytics", icon: TrendingUp, path: "/faculty/analytics" },
    ];
  } else if (currentPath.startsWith('/student')) {
    return [
      { label: "Dashboard", icon: Home, path: "/student" },
      { label: "Attendance Tracker", icon: Users, path: "/student/attendance" },
      { label: "My Analytics", icon: BarChart2, path: "/student/analytics" },
    ];
  }
  return [
    { label: "Dashboard", icon: Home, path: "/" },
  ];
};

const bottomItems = [
  { label: "Settings", icon: Settings, path: "/settings" },
  { label: "Logout", icon: LogOut, path: "/" },
];
const SidebarItem = ({ icon: Icon, label, path, isActive }) => (
  <Link
    to={path}
    className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
      isActive
        ? 'bg-white/20 text-white border-r-4 border-white'
        : 'text-white/80 hover:bg-white/10 hover:text-white'
    }`}
  >
    <Icon className="h-5 w-5 mr-3" />
    <span className="font-medium">{label}</span>
  </Link>
);

const SideBar = ({isOpen, toggleSidebar}) => {
  const location = useLocation();
  const navItems = getNavItems(location.pathname);

  return (
    <div className="w-64 h-full bg-gradient-to-b from-indigo-600 to-purple-600 text-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-white/20">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <Users className="w-5 h-5 text-white" />
          </div>
          <span className="font-bold text-lg">SmartAttendee</span>
        </div>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 px-4 py-6">
        <div className="space-y-2">
          {navItems.map((item) => (
            <SidebarItem
              key={item.label}
              {...item}
              isActive={location.pathname === item.path}
            />
          ))}
        </div>

        {/* Bottom Items */}
        <div className="absolute bottom-6 left-4 right-4 border-t border-white/20 pt-4 space-y-2">
          {bottomItems.map((item) => (
            <SidebarItem
              key={item.label}
              {...item}
              isActive={location.pathname === item.path}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SideBar;
